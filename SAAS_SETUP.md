# K-Poster SaaS Setup Guide

## Overview

K-Poster has been transformed into a complete SaaS (Software as a Service) product with the following features:

- **User Registration & Management**: Automatic user registration via Telegram
- **Payment System**: ₹20 per post with balance tracking
- **Database Storage**: Complete transaction and user data storage
- **Social Media Integration**: OAuth-based account connection
- **Multi-platform Posting**: LinkedIn, Facebook, Instagram support
- **Dashboard & Analytics**: User statistics and transaction history

## Database Schema

### Tables Created:
1. **users** - User registration and account info
2. **transactions** - Payment and transaction tracking
3. **posts** - Post history and metadata
4. **social_media_accounts** - Connected social media accounts
5. **subscriptions** - User subscription management
6. **system_settings** - Application configuration

## Setup Instructions

### 1. Database Setup

```bash
# Install PostgreSQL (if not already installed)
# On macOS:
brew install postgresql
brew services start postgresql

# Create database and user
psql postgres
CREATE DATABASE kforce;
CREATE USER postgres WITH PASSWORD 'kforce';
GRANT ALL PRIVILEGES ON DATABASE kforce TO postgres;
\q
```

### 2. Environment Variables

Update your `.env` file with the following new variables:

```env
# Existing variables...
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
XAI_API_KEY=your_xai_api_key
HUGGINGFACE_API_TOKEN=your_huggingface_token

# Database Configuration
DB_NAME=kforce
DB_USER=postgres
DB_PASS=kforce
DB_HOST=localhost
DB_PORT=5444

# Social Media OAuth
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
LINKEDIN_REDIRECT_URI=http://localhost:8000/api/oauth/linkedin/callback

FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_REDIRECT_URI=http://localhost:8000/api/oauth/facebook/callback

INSTAGRAM_CLIENT_ID=your_instagram_client_id
INSTAGRAM_CLIENT_SECRET=your_instagram_client_secret
INSTAGRAM_REDIRECT_URI=http://localhost:8000/api/oauth/instagram/callback

# Payment Configuration
PAYMENT_GATEWAY_KEY=your_payment_gateway_key
PAYMENT_GATEWAY_SECRET=your_payment_gateway_secret
POST_PRICE_INR=20.0

# Security
JWT_SECRET_KEY=your-jwt-secret-key-change-this
ENCRYPTION_KEY=your-encryption-key-change-this-32-chars
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Initialize Database

```bash
# Initialize database with tables and initial data
python migrations/init_database.py --init

# Check database status
python migrations/init_database.py --status
```

### 5. Start the Services

#### Start the API Server (for OAuth callbacks):
```bash
python api/main.py
```

#### Start the Telegram Bot:
```bash
python main.py
```

## New Bot Features

### User Commands:
- `/start` - Register and view welcome message with balance
- `/dashboard` - View account details, balance, and statistics
- `/add_funds` - Add money to account (₹100, ₹200, ₹500, etc.)
- `/connect` - Connect social media accounts via OAuth
- `/help` - Detailed help and pricing information

### Payment System:
- **₹20 per post** - Pay-as-you-go model
- **Balance tracking** - Users can add funds and track spending
- **Transaction history** - Complete audit trail
- **Subscription support** - Framework for future subscription plans

### Social Media Integration:
- **OAuth-based connection** - Secure account linking
- **Multiple platforms** - LinkedIn, Facebook, Instagram
- **Token management** - Encrypted storage of access tokens
- **Account verification** - Status tracking for connected accounts

## User Flow

1. **Registration**: User sends `/start` to bot
2. **Add Funds**: User adds money via `/add_funds`
3. **Connect Accounts**: User connects social media via `/connect`
4. **Create Post**: User sends a topic to the bot
5. **Payment Check**: Bot verifies sufficient balance
6. **Content Generation**: AI creates content and images
7. **Approval**: User reviews and approves content
8. **Platform Selection**: User selects where to post
9. **Payment Processing**: ₹20 is deducted from balance
10. **Posting**: Content is posted to selected platforms
11. **Transaction Recording**: All details saved to database

## Database Management

### Check Status:
```bash
python migrations/init_database.py --status
```

### Reset Database (WARNING - Deletes all data):
```bash
python migrations/init_database.py --reset
```

### Manual Database Operations:
```bash
# Connect to database
psql -h localhost -p 5444 -U postgres -d kforce

# View users
SELECT * FROM users;

# View transactions
SELECT * FROM transactions ORDER BY created_at DESC;

# View posts
SELECT * FROM posts ORDER BY created_at DESC;
```

## API Endpoints

The FastAPI server provides:
- `GET /` - API status and endpoints
- `GET /api/health` - Health check
- `GET /api/oauth/linkedin/callback` - LinkedIn OAuth callback
- `GET /api/oauth/facebook/callback` - Facebook OAuth callback
- `GET /api/oauth/instagram/callback` - Instagram OAuth callback
- `GET /api/oauth/status/{user_id}` - Check user's connected accounts

## Social Media App Setup

### LinkedIn App:
1. Go to [LinkedIn Developer Portal](https://developer.linkedin.com/)
2. Create a new app
3. Add redirect URI: `http://localhost:8000/api/oauth/linkedin/callback`
4. Request permissions: `r_liteprofile`, `r_emailaddress`, `w_member_social`

### Facebook App:
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Facebook Login product
4. Add redirect URI: `http://localhost:8000/api/oauth/facebook/callback`
5. Request permissions: `pages_manage_posts`, `pages_read_engagement`

### Instagram App:
1. Use Facebook app (Instagram is part of Facebook)
2. Add Instagram Basic Display product
3. Configure redirect URI: `http://localhost:8000/api/oauth/instagram/callback`

## Monitoring & Analytics

### User Statistics Available:
- Total registered users
- Active users (posted in last 30 days)
- Revenue (total transactions)
- Popular platforms
- Average posts per user
- Transaction success rates

### Database Queries for Analytics:
```sql
-- Total users
SELECT COUNT(*) FROM users;

-- Total revenue
SELECT SUM(amount) FROM transactions WHERE status = 'completed' AND transaction_type = 'payment';

-- Posts by platform
SELECT platforms_posted, COUNT(*) FROM posts GROUP BY platforms_posted;

-- Active users (last 30 days)
SELECT COUNT(DISTINCT user_id) FROM posts WHERE created_at > NOW() - INTERVAL '30 days';
```

## Production Deployment

### Environment Setup:
1. Use production database (not localhost)
2. Set proper CORS origins in FastAPI
3. Use HTTPS for OAuth callbacks
4. Set strong encryption keys
5. Configure proper logging
6. Set up database backups
7. Monitor API endpoints

### Vercel Deployment:
The existing Vercel configuration should work with the new API endpoints. Update `vercel.json` if needed for the OAuth callbacks.

## Troubleshooting

### Common Issues:
1. **Database connection failed**: Check PostgreSQL is running and credentials are correct
2. **OAuth callback errors**: Verify redirect URIs match exactly in social media apps
3. **Payment processing fails**: Check user balance and transaction logs
4. **Bot not responding**: Verify Telegram token and webhook configuration

### Logs:
- Bot logs: Check console output when running `python main.py`
- API logs: Check console output when running `python api/main.py`
- Database logs: Check PostgreSQL logs

## Next Steps

1. **Payment Gateway Integration**: Replace manual fund addition with real payment processing (Razorpay, Stripe)
2. **Subscription Plans**: Implement monthly/yearly subscription options
3. **Advanced Analytics**: Build admin dashboard for business metrics
4. **Mobile App**: Create mobile app for easier account management
5. **API Rate Limiting**: Implement rate limiting for API endpoints
6. **Email Notifications**: Send email receipts and notifications
7. **Referral System**: Add user referral program
8. **Content Templates**: Pre-built content templates for different industries
