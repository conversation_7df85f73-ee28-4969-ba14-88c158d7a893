#!/usr/bin/env python3
"""
Database initialization script for K-Poster SaaS
Creates all necessary tables and initial data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.connection import db_manager
from models.database import Base, SystemSettings
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_database_if_not_exists():
    """Create database if it doesn't exist"""
    try:
        from config import settings
        import psycopg2
        from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
        
        # Connect to PostgreSQL server (not specific database)
        conn = psycopg2.connect(
            host=settings.db_host,
            port=settings.db_port,
            user=settings.db_user,
            password=settings.db_password,
            database='postgres'  # Connect to default database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute(f"SELECT 1 FROM pg_database WHERE datname = '{settings.db_name}'")
        exists = cursor.fetchone()
        
        if not exists:
            cursor.execute(f"CREATE DATABASE {settings.db_name}")
            logger.info(f"Created database: {settings.db_name}")
        else:
            logger.info(f"Database {settings.db_name} already exists")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error creating database: {e}")
        raise

def initialize_database():
    """Initialize database with tables and initial data"""
    try:
        logger.info("Initializing database...")
        
        # Create database if it doesn't exist
        create_database_if_not_exists()
        
        # Create all tables
        logger.info("Creating tables...")
        db_manager.create_tables()
        
        # Add initial system settings
        logger.info("Adding initial system settings...")
        add_initial_settings()
        
        # Test connection
        if db_manager.test_connection():
            logger.info("✅ Database initialization completed successfully!")
        else:
            logger.error("❌ Database connection test failed")
            
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

def add_initial_settings():
    """Add initial system settings"""
    try:
        with db_manager.get_db_session() as session:
            # Check if settings already exist
            existing_settings = session.query(SystemSettings).first()
            if existing_settings:
                logger.info("System settings already exist, skipping...")
                return
            
            # Add initial settings
            settings_data = [
                {
                    "key": "post_price_inr",
                    "value": "20.0",
                    "description": "Price per post in Indian Rupees"
                },
                {
                    "key": "free_plan_monthly_limit",
                    "value": "5",
                    "description": "Monthly post limit for free plan users"
                },
                {
                    "key": "max_image_size_mb",
                    "value": "10",
                    "description": "Maximum image size in MB"
                },
                {
                    "key": "supported_platforms",
                    "value": "linkedin,facebook,instagram",
                    "description": "Comma-separated list of supported social media platforms"
                },
                {
                    "key": "maintenance_mode",
                    "value": "false",
                    "description": "Enable/disable maintenance mode"
                },
                {
                    "key": "bot_version",
                    "value": "1.0.0",
                    "description": "Current bot version"
                }
            ]
            
            for setting_data in settings_data:
                setting = SystemSettings(**setting_data)
                session.add(setting)
            
            session.commit()
            logger.info(f"Added {len(settings_data)} initial system settings")
            
    except Exception as e:
        logger.error(f"Error adding initial settings: {e}")
        raise

def reset_database():
    """Reset database (drop and recreate all tables)"""
    try:
        logger.warning("⚠️  RESETTING DATABASE - ALL DATA WILL BE LOST!")
        
        # Drop all tables
        Base.metadata.drop_all(bind=db_manager.engine)
        logger.info("Dropped all tables")
        
        # Recreate tables
        Base.metadata.create_all(bind=db_manager.engine)
        logger.info("Recreated all tables")
        
        # Add initial settings
        add_initial_settings()
        
        logger.info("✅ Database reset completed!")
        
    except Exception as e:
        logger.error(f"Database reset failed: {e}")
        raise

def check_database_status():
    """Check database status and show table information"""
    try:
        logger.info("Checking database status...")
        
        with db_manager.get_db_session() as session:
            # Check each table
            tables = [
                ("users", "SELECT COUNT(*) FROM users"),
                ("transactions", "SELECT COUNT(*) FROM transactions"),
                ("posts", "SELECT COUNT(*) FROM posts"),
                ("social_media_accounts", "SELECT COUNT(*) FROM social_media_accounts"),
                ("subscriptions", "SELECT COUNT(*) FROM subscriptions"),
                ("system_settings", "SELECT COUNT(*) FROM system_settings")
            ]
            
            logger.info("📊 Database Status:")
            for table_name, query in tables:
                try:
                    result = session.execute(text(query)).scalar()
                    logger.info(f"  {table_name}: {result} records")
                except Exception as e:
                    logger.error(f"  {table_name}: Error - {e}")
        
        logger.info("✅ Database status check completed")
        
    except Exception as e:
        logger.error(f"Database status check failed: {e}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="K-Poster Database Management")
    parser.add_argument("--init", action="store_true", help="Initialize database")
    parser.add_argument("--reset", action="store_true", help="Reset database (WARNING: Deletes all data)")
    parser.add_argument("--status", action="store_true", help="Check database status")
    
    args = parser.parse_args()
    
    if args.reset:
        confirm = input("⚠️  This will DELETE ALL DATA. Type 'RESET' to confirm: ")
        if confirm == "RESET":
            reset_database()
        else:
            logger.info("Reset cancelled")
    elif args.init:
        initialize_database()
    elif args.status:
        check_database_status()
    else:
        logger.info("Use --init to initialize, --reset to reset, or --status to check database")
        logger.info("Example: python migrations/init_database.py --init")
