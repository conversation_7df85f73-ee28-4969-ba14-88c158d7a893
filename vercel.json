{"version": 2, "builds": [{"src": "api/webhook.py", "use": "@vercel/python"}], "routes": [{"src": "/api/webhook", "dest": "/api/webhook.py"}, {"src": "/api/health", "dest": "/api/health.py"}, {"src": "/(.*)", "dest": "/api/webhook.py"}], "env": {"TELEGRAM_BOT_TOKEN": "@telegram_bot_token", "XAI_API_KEY": "@xai_api_key", "HUGGINGFACE_API_TOKEN": "@huggingface_api_token", "LINKEDIN_CLIENT_ID": "@linkedin_client_id", "LINKEDIN_CLIENT_SECRET": "@linkedin_client_secret", "LINKEDIN_ACCESS_TOKEN": "@linkedin_access_token", "FACEBOOK_ACCESS_TOKEN": "@facebook_access_token", "FACEBOOK_PAGE_ID": "@facebook_page_id", "INSTAGRAM_USERNAME": "@instagram_username", "INSTAGRAM_PASSWORD": "@instagram_password", "VERCEL_DEPLOYMENT": "true"}, "functions": {"api/webhook.py": {"maxDuration": 300}}}