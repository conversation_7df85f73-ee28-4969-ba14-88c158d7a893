from sqlalchemy import Column, Integer, String, DateTime, <PERSON>olean, Float, Text, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    telegram_user_id = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, nullable=True)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    email = Column(String, unique=True, nullable=True)
    phone = Column(String, nullable=True)
    
    # Account status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    registration_date = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    
    # Subscription and balance
    balance = Column(Float, default=0.0)  # Credits in INR
    total_posts = Column(Integer, default=0)
    subscription_type = Column(String, default="free")  # free, premium
    subscription_expires = Column(DateTime, nullable=True)
    
    # Relationships
    transactions = relationship("Transaction", back_populates="user")
    posts = relationship("Post", back_populates="user")
    social_accounts = relationship("SocialMediaAccount", back_populates="user")

class Transaction(Base):
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    transaction_id = Column(String, unique=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Transaction details
    amount = Column(Float, nullable=False)
    currency = Column(String, default="INR")
    transaction_type = Column(String, nullable=False)  # payment, refund, credit
    status = Column(String, default="pending")  # pending, completed, failed, refunded
    
    # Payment gateway details
    payment_gateway = Column(String, nullable=True)  # razorpay, stripe, etc.
    gateway_transaction_id = Column(String, nullable=True)
    gateway_response = Column(JSON, nullable=True)
    
    # Metadata
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="transactions")

class Post(Base):
    __tablename__ = "posts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Post content
    topic = Column(String, nullable=False)
    short_post = Column(Text, nullable=True)
    long_post = Column(Text, nullable=True)
    hashtags = Column(JSON, nullable=True)  # List of hashtags
    key_insights = Column(JSON, nullable=True)  # List of insights
    
    # Image details
    image_description = Column(Text, nullable=True)
    image_url = Column(String, nullable=True)
    image_path = Column(String, nullable=True)
    
    # Post metadata
    status = Column(String, default="draft")  # draft, approved, posted, failed
    platforms_posted = Column(JSON, nullable=True)  # List of platforms where posted
    posting_results = Column(JSON, nullable=True)  # Results from each platform
    
    # Pricing
    cost = Column(Float, default=20.0)
    transaction_id = Column(Integer, ForeignKey("transactions.id"), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    posted_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="posts")
    transaction = relationship("Transaction")

class SocialMediaAccount(Base):
    __tablename__ = "social_media_accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Platform details
    platform = Column(String, nullable=False)  # linkedin, facebook, instagram
    platform_user_id = Column(String, nullable=True)
    username = Column(String, nullable=True)
    display_name = Column(String, nullable=True)
    
    # Authentication
    access_token = Column(Text, nullable=True)  # Encrypted
    refresh_token = Column(Text, nullable=True)  # Encrypted
    token_expires_at = Column(DateTime, nullable=True)
    
    # Account status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    last_used = Column(DateTime, nullable=True)
    
    # Metadata
    account_data = Column(JSON, nullable=True)  # Additional platform-specific data
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="social_accounts")

class Subscription(Base):
    __tablename__ = "subscriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Subscription details
    plan_name = Column(String, nullable=False)  # free, basic, premium
    plan_price = Column(Float, default=0.0)
    billing_cycle = Column(String, default="monthly")  # monthly, yearly
    
    # Status
    status = Column(String, default="active")  # active, cancelled, expired
    starts_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)
    auto_renew = Column(Boolean, default=False)
    
    # Limits
    monthly_post_limit = Column(Integer, default=5)  # Posts per month
    posts_used_this_month = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User")

class SystemSettings(Base):
    __tablename__ = "system_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String, unique=True, nullable=False)
    value = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
