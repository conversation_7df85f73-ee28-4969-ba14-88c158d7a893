#!/usr/bin/env python3
"""
K-Poster SaaS: AI-Powered Social Media Content Generator

This SaaS application creates engaging social media posts by:
1. User registration and payment processing (₹20 per post)
2. Taking topic input from Telegram
3. Crawling content from multiple sources (Medium, Quora, Reddit)
4. Using AI (Grok) to generate summaries and image descriptions
5. Creating AI-generated images
6. Posting to multiple social media platforms
7. Storing all transactions and user data in database

Features:
- User registration and authentication
- Payment processing and balance management
- Social media account integration (OAuth)
- Complete transaction tracking
- User dashboard and analytics

Author: K-Poster Team
Version: 2.0.0 (SaaS)
"""

import asyncio
import sys
from loguru import logger
from config import settings, validate_config
from telegram_bot import TelegramBot
from database.connection import db_manager

def setup_logging():
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    # Add console handler
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # Add file handler
    logger.add(
        "logs/k_poster.log",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days",
        compression="zip"
    )

def create_directories():
    """Create necessary directories"""
    import os
    
    directories = [
        "logs",
        "generated_images",
        "temp"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def check_dependencies():
    """Check if all required dependencies are available"""
    try:
        import telegram
        import requests
        import beautifulsoup4
        import openai
        import PIL
        logger.info("All required dependencies are available")
        return True
    except ImportError as e:
        logger.error(f"Missing dependency: {e}")
        logger.error("Please install dependencies with: pip install -r requirements.txt")
        return False

def check_database():
    """Check database connection and setup"""
    try:
        logger.info("🗄️ Checking database connection...")

        if not db_manager.test_connection():
            logger.error("❌ Database connection failed!")
            logger.error("Please ensure PostgreSQL is running and database is configured correctly.")
            logger.error("Run: python migrations/init_database.py --init")
            return False

        logger.info("✅ Database connection successful")
        return True

    except Exception as e:
        logger.error(f"❌ Database check failed: {e}")
        return False

def main():
    """Main application entry point"""
    logger.info("🚀 Starting K-Poster SaaS Application")

    try:
        # Setup
        setup_logging()
        create_directories()

        # Check dependencies
        if not check_dependencies():
            logger.error("❌ Dependency check failed")
            sys.exit(1)

        # Check database
        if not check_database():
            logger.error("❌ Database check failed")
            sys.exit(1)

        # Validate configuration
        logger.info("🔧 Validating configuration...")
        validate_config()
        logger.info("✅ Configuration validated successfully")

        # Initialize and run Telegram bot
        logger.info("🤖 Initializing K-Poster SaaS Telegram bot...")
        bot = TelegramBot()

        logger.info("✅ K-Poster SaaS is ready!")
        logger.info("💰 Users can now register, add funds, and create posts!")
        logger.info("📱 Send /start to your Telegram bot to begin!")

        # Run the bot
        bot.run()

    except KeyboardInterrupt:
        logger.info("👋 K-Poster SaaS stopped by user")
        db_manager.close()
        sys.exit(0)

    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        db_manager.close()
        sys.exit(1)

if __name__ == "__main__":
    main()
