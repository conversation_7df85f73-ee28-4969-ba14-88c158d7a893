from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
import logging
from contextlib import asynccontextmanager

# Import existing API modules
from api.health import router as health_router
from api.webhook import router as webhook_router
from api.oauth_callbacks import router as oauth_router

# Import database
from database.connection import db_manager

logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting K-Poster SaaS API...")
    
    # Test database connection
    if not db_manager.test_connection():
        logger.error("Database connection failed!")
        raise Exception("Database connection failed")
    
    logger.info("Database connection successful")
    yield
    
    # Shutdown
    logger.info("Shutting down K-Poster SaaS API...")
    db_manager.close()

# Create FastAPI app
app = FastAPI(
    title="K-Poster SaaS API",
    description="API for K-Poster Social Media Automation SaaS",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health_router, prefix="/api", tags=["health"])
app.include_router(webhook_router, prefix="/api", tags=["webhook"])
app.include_router(oauth_router, prefix="/api", tags=["oauth"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "K-Poster SaaS API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "health": "/api/health",
            "webhook": "/api/webhook",
            "oauth_linkedin": "/api/oauth/linkedin/callback",
            "oauth_facebook": "/api/oauth/facebook/callback",
            "oauth_instagram": "/api/oauth/instagram/callback"
        }
    }

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Global exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
