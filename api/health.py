import json
import os
import sys

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def handler(request):
    """Health check endpoint"""
    try:
        # Basic health check
        health_status = {
            "status": "healthy",
            "service": "K-Poster Bot",
            "version": "1.0.0",
            "environment": "vercel",
            "timestamp": str(__import__('datetime').datetime.utcnow()),
            "checks": {
                "environment_variables": check_env_vars(),
                "imports": check_imports()
            }
        }
        
        # Determine overall status
        all_checks_passed = all(health_status["checks"].values())
        health_status["status"] = "healthy" if all_checks_passed else "unhealthy"
        
        status_code = 200 if all_checks_passed else 503
        
        return {
            'statusCode': status_code,
            'headers': {
                'Content-Type': 'application/json',
            },
            'body': json.dumps(health_status, indent=2)
        }
        
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
            },
            'body': json.dumps({
                'status': 'error',
                'message': str(e)
            })
        }

def check_env_vars():
    """Check if required environment variables are set"""
    required_vars = [
        'TELEGRAM_BOT_TOKEN',
        'XAI_API_KEY',
        'HUGGINGFACE_API_TOKEN'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    return len(missing_vars) == 0

def check_imports():
    """Check if required modules can be imported"""
    try:
        import telegram
        import requests
        import openai
        from bs4 import BeautifulSoup
        from PIL import Image
        return True
    except ImportError:
        return False

# Vercel entry point
def main(request):
    return handler(request)
