from fastapi import APIRout<PERSON>, Request, HTTPException
from fastapi.responses import HTMLResponse
from services.social_auth_service import social_auth_service
from services.user_service import user_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/oauth/linkedin/callback")
async def linkedin_callback(request: Request):
    """Handle LinkedIn OAuth callback"""
    try:
        # Get parameters from query string
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        error = request.query_params.get("error")
        
        if error:
            return HTMLResponse(f"""
            <html>
                <body>
                    <h2>❌ LinkedIn Connection Failed</h2>
                    <p>Error: {error}</p>
                    <p>Please try again from the Telegram bot.</p>
                </body>
            </html>
            """)
        
        if not code or not state:
            raise HTTPException(status_code=400, detail="Missing code or state parameter")
        
        # Process the callback
        result = social_auth_service.handle_linkedin_callback(code, state)
        
        if result["success"]:
            return HTMLResponse("""
            <html>
                <body>
                    <h2>✅ LinkedIn Connected Successfully!</h2>
                    <p>Your LinkedIn account has been connected to K-Poster.</p>
                    <p>You can now return to the Telegram bot to create posts.</p>
                    <script>
                        setTimeout(function() {
                            window.close();
                        }, 3000);
                    </script>
                </body>
            </html>
            """)
        else:
            return HTMLResponse(f"""
            <html>
                <body>
                    <h2>❌ LinkedIn Connection Failed</h2>
                    <p>Error: {result.get('error', 'Unknown error')}</p>
                    <p>Please try again from the Telegram bot.</p>
                </body>
            </html>
            """)
            
    except Exception as e:
        logger.error(f"LinkedIn callback error: {e}")
        return HTMLResponse(f"""
        <html>
            <body>
                <h2>❌ Connection Error</h2>
                <p>An error occurred while connecting your LinkedIn account.</p>
                <p>Error: {str(e)}</p>
                <p>Please try again from the Telegram bot.</p>
            </body>
        </html>
        """)

@router.get("/oauth/facebook/callback")
async def facebook_callback(request: Request):
    """Handle Facebook OAuth callback"""
    try:
        # Get parameters from query string
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        error = request.query_params.get("error")
        
        if error:
            return HTMLResponse(f"""
            <html>
                <body>
                    <h2>❌ Facebook Connection Failed</h2>
                    <p>Error: {error}</p>
                    <p>Please try again from the Telegram bot.</p>
                </body>
            </html>
            """)
        
        if not code or not state:
            raise HTTPException(status_code=400, detail="Missing code or state parameter")
        
        # Process the callback
        result = social_auth_service.handle_facebook_callback(code, state)
        
        if result["success"]:
            return HTMLResponse("""
            <html>
                <body>
                    <h2>✅ Facebook Connected Successfully!</h2>
                    <p>Your Facebook account has been connected to K-Poster.</p>
                    <p>You can now return to the Telegram bot to create posts.</p>
                    <script>
                        setTimeout(function() {
                            window.close();
                        }, 3000);
                    </script>
                </body>
            </html>
            """)
        else:
            return HTMLResponse(f"""
            <html>
                <body>
                    <h2>❌ Facebook Connection Failed</h2>
                    <p>Error: {result.get('error', 'Unknown error')}</p>
                    <p>Please try again from the Telegram bot.</p>
                </body>
            </html>
            """)
            
    except Exception as e:
        logger.error(f"Facebook callback error: {e}")
        return HTMLResponse(f"""
        <html>
            <body>
                <h2>❌ Connection Error</h2>
                <p>An error occurred while connecting your Facebook account.</p>
                <p>Error: {str(e)}</p>
                <p>Please try again from the Telegram bot.</p>
            </body>
        </html>
        """)

@router.get("/oauth/instagram/callback")
async def instagram_callback(request: Request):
    """Handle Instagram OAuth callback"""
    try:
        # Get parameters from query string
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        error = request.query_params.get("error")
        
        if error:
            return HTMLResponse(f"""
            <html>
                <body>
                    <h2>❌ Instagram Connection Failed</h2>
                    <p>Error: {error}</p>
                    <p>Please try again from the Telegram bot.</p>
                </body>
            </html>
            """)
        
        if not code or not state:
            raise HTTPException(status_code=400, detail="Missing code or state parameter")
        
        # Instagram OAuth handling would go here
        # For now, return a placeholder
        return HTMLResponse("""
        <html>
            <body>
                <h2>🚧 Instagram Integration Coming Soon</h2>
                <p>Instagram integration is currently under development.</p>
                <p>Please use LinkedIn or Facebook for now.</p>
            </body>
        </html>
        """)
            
    except Exception as e:
        logger.error(f"Instagram callback error: {e}")
        return HTMLResponse(f"""
        <html>
            <body>
                <h2>❌ Connection Error</h2>
                <p>An error occurred while connecting your Instagram account.</p>
                <p>Error: {str(e)}</p>
                <p>Please try again from the Telegram bot.</p>
            </body>
        </html>
        """)

@router.get("/oauth/status/{user_id}")
async def check_oauth_status(user_id: int):
    """Check OAuth connection status for a user"""
    try:
        accounts = social_auth_service.get_user_social_accounts(user_id)
        return {
            "user_id": user_id,
            "connected_accounts": accounts,
            "total_connected": len(accounts)
        }
    except Exception as e:
        logger.error(f"Error checking OAuth status for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
