import json
import os
import sys
import asyncio
from typing import Dict, Any

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from telegram import Update
from telegram.ext import Application
from loguru import logger
import tempfile

# Import our modules
from web_crawler import WebCrawler
from ai_content_generator import <PERSON><PERSON><PERSON>ntGenerator
from image_generator import ImageGenerator
from social_media_poster import SocialMediaPoster
from config import settings

class VercelTelegramBot:
    def __init__(self):
        self.token = settings.telegram_bot_token
        self.crawler = WebCrawler()
        self.ai_generator = AIContentGenerator()
        self.image_generator = ImageGenerator()
        self.social_poster = SocialMediaPoster()
        
        # Initialize the application
        self.application = Application.builder().token(self.token).build()
        self._setup_handlers()
    
    def _setup_handlers(self):
        """Setup all the handlers for the bot"""
        from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CallbackQueryHandler, filters
        from telegram_bot_handlers import TelegramBotHandlers
        
        handlers = TelegramBotHandlers(
            self.crawler, 
            self.ai_generator, 
            self.image_generator, 
            self.social_poster
        )
        
        # Add handlers
        self.application.add_handler(CommandHandler("start", handlers.start_command))
        self.application.add_handler(CommandHandler("help", handlers.help_command))
        self.application.add_handler(CommandHandler("cancel", handlers.cancel_command))
        self.application.add_handler(CallbackQueryHandler(handlers.handle_callback_query))
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handlers.handle_topic_input))
    
    async def process_update(self, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single update from Telegram"""
        try:
            # Create Update object from the webhook data
            update = Update.de_json(update_data, self.application.bot)
            
            # Process the update
            await self.application.process_update(update)
            
            return {"status": "success", "message": "Update processed"}
            
        except Exception as e:
            logger.error(f"Error processing update: {e}")
            return {"status": "error", "message": str(e)}

# Global bot instance
bot_instance = None

def get_bot_instance():
    """Get or create bot instance"""
    global bot_instance
    if bot_instance is None:
        bot_instance = VercelTelegramBot()
    return bot_instance

async def handler(request):
    """Main webhook handler for Vercel"""
    try:
        # Parse the request
        if hasattr(request, 'get_json'):
            # Flask-like request object
            update_data = request.get_json()
        elif hasattr(request, 'json'):
            # FastAPI-like request object
            update_data = await request.json()
        else:
            # Raw request data
            import json
            body = request.get('body', '{}')
            if isinstance(body, bytes):
                body = body.decode('utf-8')
            update_data = json.loads(body)
        
        # Get bot instance and process update
        bot = get_bot_instance()
        result = await bot.process_update(update_data)
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
            },
            'body': json.dumps(result)
        }
        
    except Exception as e:
        logger.error(f"Webhook error: {e}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
            },
            'body': json.dumps({
                'status': 'error',
                'message': str(e)
            })
        }

# Vercel serverless function entry point
def main(request):
    """Vercel entry point"""
    return asyncio.run(handler(request))
