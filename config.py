import os
from dotenv import load_dotenv
from pydantic import BaseSettings
from typing import Optional

load_dotenv()

class Settings(BaseSettings):
    # Telegram Configuration
    telegram_bot_token: str = os.getenv("TELEGRAM_BOT_TOKEN", "")

    # xAI Grok Configuration
    xai_api_key: str = os.getenv("XAI_API_KEY", "")
    xai_base_url: str = os.getenv("XAI_BASE_URL", "https://api.x.ai/v1")

    # Hugging Face Configuration
    huggingface_api_token: str = os.getenv("HUGGINGFACE_API_TOKEN", "")

    # Database Configuration
    db_name: str = os.getenv("DB_NAME", "kforce")
    db_user: str = os.getenv("DB_USER", "postgres")
    db_password: str = os.getenv("DB_PASS", "kforce")
    db_host: str = os.getenv("DB_HOST", "localhost")
    db_port: int = int(os.getenv("DB_PORT", "5444"))

    # Social Media Configuration
    linkedin_client_id: str = os.getenv("LINKEDIN_CLIENT_ID", "")
    linkedin_client_secret: str = os.getenv("LINKEDIN_CLIENT_SECRET", "")
    linkedin_redirect_uri: str = os.getenv("LINKEDIN_REDIRECT_URI", "")

    facebook_app_id: str = os.getenv("FACEBOOK_APP_ID", "")
    facebook_app_secret: str = os.getenv("FACEBOOK_APP_SECRET", "")
    facebook_redirect_uri: str = os.getenv("FACEBOOK_REDIRECT_URI", "")

    instagram_client_id: str = os.getenv("INSTAGRAM_CLIENT_ID", "")
    instagram_client_secret: str = os.getenv("INSTAGRAM_CLIENT_SECRET", "")
    instagram_redirect_uri: str = os.getenv("INSTAGRAM_REDIRECT_URI", "")

    # Payment Configuration
    payment_gateway_key: str = os.getenv("PAYMENT_GATEWAY_KEY", "")
    payment_gateway_secret: str = os.getenv("PAYMENT_GATEWAY_SECRET", "")
    post_price_inr: float = float(os.getenv("POST_PRICE_INR", "20.0"))

    # Application Settings
    debug: bool = os.getenv("DEBUG", "True").lower() == "true"
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    max_crawl_pages: int = int(os.getenv("MAX_CRAWL_PAGES", "5"))
    image_generation_model: str = os.getenv("IMAGE_GENERATION_MODEL", "stabilityai/stable-diffusion-2-1")

    # Security
    jwt_secret_key: str = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-this")
    encryption_key: str = os.getenv("ENCRYPTION_KEY", "your-encryption-key-change-this")
    
    class Config:
        env_file = ".env"

settings = Settings()

# Validation
def validate_config():
    """Validate that required configuration is present"""
    required_fields = [
        ("telegram_bot_token", "Telegram Bot Token"),
        ("xai_api_key", "xAI API Key"),
        ("huggingface_api_token", "Hugging Face API Token")
    ]
    
    missing_fields = []
    for field, description in required_fields:
        if not getattr(settings, field):
            missing_fields.append(description)
    
    if missing_fields:
        raise ValueError(f"Missing required configuration: {', '.join(missing_fields)}")
    
    return True
