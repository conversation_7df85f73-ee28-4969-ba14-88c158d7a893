# K-Poster Vercel Deployment Guide

This guide will help you deploy the K-Poster application to Vercel as a serverless function.

## 🚨 Important Notes

**Vercel Limitations for Telegram Bots:**
- Vercel functions have a **10-second execution limit** on the free plan
- **15-minute timeout** on Pro plan
- **No persistent storage** between function calls
- **Cold starts** may cause delays

**Recommended Alternatives:**
- **Railway** - Better for long-running applications
- **Heroku** - Traditional hosting with persistent processes
- **DigitalOcean App Platform** - Good for containerized apps
- **AWS Lambda** with longer timeouts

## 📋 Prerequisites

1. **Vercel Account** - Sign up at [vercel.com](https://vercel.com)
2. **Vercel CLI** - Install with `npm install -g vercel`
3. **All API Keys** - Telegram, xAI Grok, Hugging Face, Social Media APIs
4. **Git Repository** - Your code should be in a Git repository

## 🛠️ Deployment Steps

### 1. Prepare Your Project

```bash
# Ensure you're in the project directory
cd K-poster

# Install Vercel CLI if not already installed
npm install -g vercel

# Login to Vercel
vercel login
```

### 2. Configure Environment Variables

Create a `.env.production` file with your production API keys:

```env
TELEGRAM_BOT_TOKEN=your_production_telegram_bot_token
XAI_API_KEY=your_xai_api_key
HUGGINGFACE_API_TOKEN=your_huggingface_token
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
LINKEDIN_ACCESS_TOKEN=your_linkedin_access_token
FACEBOOK_ACCESS_TOKEN=your_facebook_access_token
FACEBOOK_PAGE_ID=your_facebook_page_id
INSTAGRAM_USERNAME=your_instagram_username
INSTAGRAM_PASSWORD=your_instagram_password
VERCEL_DEPLOYMENT=true
```

### 3. Deploy to Vercel

```bash
# Deploy the application
vercel

# Follow the prompts:
# - Set up and deploy? Y
# - Which scope? (select your account)
# - Link to existing project? N (for first deployment)
# - Project name: k-poster (or your preferred name)
# - Directory: ./ (current directory)
```

### 4. Set Environment Variables in Vercel

You can set environment variables in two ways:

#### Option A: Via Vercel Dashboard
1. Go to your project dashboard on vercel.com
2. Navigate to Settings → Environment Variables
3. Add each variable from your `.env.production` file

#### Option B: Via Vercel CLI
```bash
# Set environment variables
vercel env add TELEGRAM_BOT_TOKEN
vercel env add XAI_API_KEY
vercel env add HUGGINGFACE_API_TOKEN
# ... add all other variables

# Deploy with new environment variables
vercel --prod
```

### 5. Setup Telegram Webhook

After deployment, you'll get a Vercel URL (e.g., `https://k-poster.vercel.app`).

Run the setup script:

```bash
python deploy_setup.py
```

Or manually set the webhook:

```bash
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://your-app.vercel.app/api/webhook"}'
```

### 6. Test Your Deployment

1. **Health Check**: Visit `https://your-app.vercel.app/api/health`
2. **Telegram Test**: Send a message to your bot
3. **Monitor Logs**: Check Vercel dashboard for function logs

## 📁 Vercel Project Structure

```
K-poster/
├── api/
│   ├── webhook.py          # Main webhook handler
│   └── health.py           # Health check endpoint
├── vercel.json             # Vercel configuration
├── requirements-vercel.txt # Vercel-specific dependencies
├── telegram_bot_handlers.py # Separated handlers for serverless
└── ... (other files)
```

## 🔧 Configuration Files

### vercel.json
- Defines serverless functions
- Sets up routing
- Configures environment variables
- Sets function timeout limits

### api/webhook.py
- Main entry point for Telegram webhooks
- Handles incoming updates
- Processes bot commands and messages

### api/health.py
- Health check endpoint
- Verifies environment variables
- Tests module imports

## 🐛 Troubleshooting

### Common Issues

1. **Function Timeout**
   ```
   Error: Function execution timed out
   ```
   **Solution**: Optimize code, reduce API calls, or use a different hosting platform

2. **Cold Start Delays**
   ```
   Bot responds slowly on first message
   ```
   **Solution**: This is normal for serverless functions. Consider using Vercel Pro for faster cold starts.

3. **Environment Variables Not Found**
   ```
   Error: Missing required configuration
   ```
   **Solution**: Verify all environment variables are set in Vercel dashboard

4. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'xyz'
   ```
   **Solution**: Check `requirements-vercel.txt` includes all dependencies

5. **Webhook Not Working**
   ```
   Bot doesn't respond to messages
   ```
   **Solution**: 
   - Check webhook URL is correct
   - Verify function logs in Vercel dashboard
   - Test health endpoint

### Debugging Commands

```bash
# Check webhook status
curl "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getWebhookInfo"

# Test health endpoint
curl "https://your-app.vercel.app/api/health"

# View function logs
vercel logs

# Redeploy
vercel --prod
```

## 📊 Monitoring

### Vercel Dashboard
- Function invocations
- Error rates
- Response times
- Bandwidth usage

### Telegram Bot Monitoring
```python
# Add to your webhook handler
import time
start_time = time.time()
# ... process update ...
processing_time = time.time() - start_time
logger.info(f"Processing time: {processing_time:.2f}s")
```

## 🔄 Alternative Deployment Options

If Vercel doesn't meet your needs:

### Railway (Recommended)
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

### Heroku
```bash
# Install Heroku CLI
# Create Procfile: web: python main.py
heroku create k-poster
git push heroku main
```

### DigitalOcean App Platform
- Use the web interface
- Connect your Git repository
- Configure environment variables
- Deploy

## 🔒 Security Considerations

1. **Environment Variables**: Never commit API keys to Git
2. **Webhook Security**: Consider adding webhook secret validation
3. **Rate Limiting**: Implement rate limiting for API calls
4. **Error Handling**: Don't expose sensitive information in error messages

## 📈 Scaling Considerations

1. **Function Limits**: Vercel has execution time and memory limits
2. **API Rate Limits**: Monitor API usage across all services
3. **Cost Management**: Track function invocations and bandwidth
4. **Performance**: Optimize for cold start times

## 🆘 Support

If you encounter issues:

1. Check Vercel function logs
2. Test the health endpoint
3. Verify webhook configuration
4. Review environment variables
5. Check API rate limits

For persistent issues, consider migrating to a traditional hosting platform like Railway or Heroku.
