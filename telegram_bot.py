import asyncio
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, ContextTypes, filters
from loguru import logger
from config import settings
import json
import io
from typing import Dict, List
import base64
from datetime import datetime

from web_crawler import WebCrawler
from ai_content_generator import AIContentGenerator
from image_generator import ImageGenerator
from social_media_poster import SocialMediaPoster

# Import new services
from services.user_service import user_service
from services.payment_service import payment_service
from services.social_auth_service import social_auth_service
from models.database import Post
from database.connection import get_db_context

class TelegramBot:
    def __init__(self):
        self.token = settings.telegram_bot_token
        self.crawler = WebCrawler()
        self.ai_generator = AIContentGenerator()
        self.image_generator = ImageGenerator()
        self.social_poster = SocialMediaPoster()
        
        # Store user sessions
        self.user_sessions = {}
        
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command with user registration"""
        telegram_user = update.effective_user

        # Register or get existing user
        user_data = {
            'username': telegram_user.username,
            'first_name': telegram_user.first_name,
            'last_name': telegram_user.last_name
        }

        user = user_service.get_or_create_user(str(telegram_user.id), user_data)
        user_stats = user_service.get_user_stats(user.id)

        # Create welcome message with user info
        welcome_message = f"""
🚀 Welcome to K-Poster SaaS Bot, {user.first_name or 'User'}!

💰 Your Balance: ₹{user.balance:.2f}
📊 Total Posts: {user_stats['total_posts']}
📅 Member Since: {user.registration_date.strftime('%B %Y')}

🎯 **How it works:**
• Each post costs ₹{settings.post_price_inr}
• Research trending topics automatically
• AI-powered content generation
• Custom image creation
• Multi-platform posting

📱 **Commands:**
/dashboard - View your account details
/add_funds - Add money to your account
/connect - Connect social media accounts
/help - Get detailed help

💡 Send me a topic to create your first post!
Example: "artificial intelligence trends"
        """

        # Create action buttons
        keyboard = [
            [
                InlineKeyboardButton("💰 Add Funds", callback_data="add_funds"),
                InlineKeyboardButton("📊 Dashboard", callback_data="dashboard")
            ],
            [
                InlineKeyboardButton("🔗 Connect Accounts", callback_data="connect_accounts"),
                InlineKeyboardButton("📖 Help", callback_data="help")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(welcome_message, reply_markup=reply_markup, parse_mode="Markdown")
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_message = """
📖 How to use K-Poster Bot:

1. Send me any topic (e.g., "blockchain technology")
2. I'll research the topic from multiple sources
3. AI will generate engaging content and image
4. You can preview and approve the content
5. Choose platforms to post (LinkedIn, Facebook, Instagram)
6. I'll post it for you!

Commands:
/start - Start the bot
/help - Show this help message
/cancel - Cancel current operation

Just send me a topic to begin! 🚀
        """
        
        await update.message.reply_text(help_message)
    
    async def cancel_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /cancel command"""
        user_id = update.effective_user.id
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]

        await update.message.reply_text("❌ Operation cancelled. Send me a new topic to start over!")

    async def dashboard_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show user dashboard"""
        telegram_user = update.effective_user
        user = user_service.get_user_by_telegram_id(str(telegram_user.id))

        if not user:
            await update.message.reply_text("❌ User not found. Please use /start to register.")
            return

        user_stats = user_service.get_user_stats(user.id)
        recent_transactions = payment_service.get_user_transactions(user.id, limit=5)
        connected_accounts = social_auth_service.get_user_social_accounts(user.id)

        # Format dashboard message
        dashboard_message = f"""
📊 **Your Dashboard**

👤 **Account Info:**
• Name: {user.first_name} {user.last_name or ''}
• Username: @{user.username or 'Not set'}
• Member Since: {user.registration_date.strftime('%B %d, %Y')}

💰 **Balance & Usage:**
• Current Balance: ₹{user.balance:.2f}
• Total Posts Created: {user_stats['total_posts']}
• Successful Posts: {user_stats['successful_posts']}
• Posts This Month: {user_stats['posts_this_month']}
• Total Spent: ₹{user_stats['total_spent']:.2f}

🔗 **Connected Accounts:**
{self._format_connected_accounts(connected_accounts)}

📈 **Recent Transactions:**
{self._format_recent_transactions(recent_transactions)}
        """

        # Create action buttons
        keyboard = [
            [
                InlineKeyboardButton("💰 Add Funds", callback_data="add_funds"),
                InlineKeyboardButton("🔗 Connect Accounts", callback_data="connect_accounts")
            ],
            [
                InlineKeyboardButton("📜 Transaction History", callback_data="transaction_history"),
                InlineKeyboardButton("🔄 Refresh", callback_data="dashboard")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(dashboard_message, reply_markup=reply_markup, parse_mode="Markdown")

    def _format_connected_accounts(self, accounts: List[Dict]) -> str:
        """Format connected accounts for display"""
        if not accounts:
            return "• No accounts connected yet"

        formatted = []
        for account in accounts:
            platform_emoji = {"linkedin": "💼", "facebook": "📘", "instagram": "📷"}.get(account['platform'], "🔗")
            status = "✅" if account['is_verified'] else "⚠️"
            formatted.append(f"• {platform_emoji} {account['platform'].title()}: {account['display_name']} {status}")

        return "\n".join(formatted)

    def _format_recent_transactions(self, transactions: List[Dict]) -> str:
        """Format recent transactions for display"""
        if not transactions:
            return "• No transactions yet"

        formatted = []
        for txn in transactions[:3]:  # Show only last 3
            type_emoji = {"payment": "💳", "deduction": "💸", "refund": "💰"}.get(txn['type'], "💱")
            status_emoji = {"completed": "✅", "pending": "⏳", "failed": "❌"}.get(txn['status'], "❓")
            formatted.append(f"• {type_emoji} ₹{txn['amount']:.2f} - {txn['description'][:30]}... {status_emoji}")

        return "\n".join(formatted)

    async def add_funds_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add funds command"""
        telegram_user = update.effective_user
        user = user_service.get_user_by_telegram_id(str(telegram_user.id))

        if not user:
            await update.message.reply_text("❌ User not found. Please use /start to register.")
            return

        add_funds_message = f"""
💰 **Add Funds to Your Account**

Current Balance: ₹{user.balance:.2f}

Select an amount to add:
        """

        # Create amount selection buttons
        keyboard = [
            [
                InlineKeyboardButton("₹100", callback_data="add_funds_100"),
                InlineKeyboardButton("₹200", callback_data="add_funds_200"),
                InlineKeyboardButton("₹500", callback_data="add_funds_500")
            ],
            [
                InlineKeyboardButton("₹1000", callback_data="add_funds_1000"),
                InlineKeyboardButton("₹2000", callback_data="add_funds_2000"),
                InlineKeyboardButton("₹5000", callback_data="add_funds_5000")
            ],
            [InlineKeyboardButton("🔙 Back to Dashboard", callback_data="dashboard")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(add_funds_message, reply_markup=reply_markup, parse_mode="Markdown")

    async def connect_accounts_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle connect social accounts command"""
        telegram_user = update.effective_user
        user = user_service.get_user_by_telegram_id(str(telegram_user.id))

        if not user:
            await update.message.reply_text("❌ User not found. Please use /start to register.")
            return

        connected_accounts = social_auth_service.get_user_social_accounts(user.id)

        connect_message = f"""
🔗 **Connect Social Media Accounts**

Connect your social media accounts to post directly:

**Currently Connected:**
{self._format_connected_accounts(connected_accounts)}

Select a platform to connect:
        """

        # Create platform connection buttons
        keyboard = [
            [
                InlineKeyboardButton("💼 LinkedIn", callback_data="connect_linkedin"),
                InlineKeyboardButton("📘 Facebook", callback_data="connect_facebook")
            ],
            [
                InlineKeyboardButton("📷 Instagram", callback_data="connect_instagram")
            ],
            [InlineKeyboardButton("🔙 Back to Dashboard", callback_data="dashboard")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(connect_message, reply_markup=reply_markup, parse_mode="Markdown")
    
    async def handle_topic_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle user topic input with payment validation"""
        telegram_user = update.effective_user
        user = user_service.get_user_by_telegram_id(str(telegram_user.id))

        if not user:
            await update.message.reply_text("❌ Please use /start to register first.")
            return

        topic = update.message.text.strip()

        if len(topic) < 3:
            await update.message.reply_text("Please provide a more specific topic (at least 3 characters).")
            return

        # Check if user can create post (has balance or subscription)
        can_create = user_service.can_create_post(user.id, settings.post_price_inr)

        if not can_create["can_create"]:
            insufficient_message = f"""
❌ **Insufficient Funds**

{payment_service._get_insufficient_funds_message(can_create)}

💡 **Options:**
• Use /add_funds to add money to your account
• Each post costs ₹{settings.post_price_inr}

Current Balance: ₹{user.balance:.2f}
            """

            keyboard = [
                [InlineKeyboardButton("💰 Add Funds", callback_data="add_funds")],
                [InlineKeyboardButton("📊 Dashboard", callback_data="dashboard")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(insufficient_message, reply_markup=reply_markup, parse_mode="Markdown")
            return

        # Initialize user session
        self.user_sessions[user.id] = {
            "topic": topic,
            "step": "processing",
            "user_id": user.id,
            "can_create_result": can_create
        }
        
        # Send processing message
        processing_msg = await update.message.reply_text(
            f"🔍 Processing your request for: *{topic}*\n\n"
            "⏳ This may take a few minutes...\n"
            "📊 Crawling sources...",
            parse_mode="Markdown"
        )

        try:
            # Step 1: Crawl content
            await processing_msg.edit_text(
                f"🔍 Processing: *{topic}*\n\n"
                "📊 Crawling sources... ✅\n"
                "🤖 Generating content...",
                parse_mode="Markdown"
            )

            articles = self.crawler.crawl_all_sources(topic)

            if not articles:
                await processing_msg.edit_text(
                    "❌ Sorry, I couldn't find enough content about this topic. Please try a different topic."
                )
                return

            # Step 2: Generate content
            await processing_msg.edit_text(
                f"🔍 Processing: *{topic}*\n\n"
                "📊 Crawling sources... ✅\n"
                "🤖 Generating content... ✅\n"
                "🎨 Creating image...",
                parse_mode="Markdown"
            )

            content_summary = self.ai_generator.generate_content_summary(articles, topic)

            # Step 3: Generate image
            image_description = self.ai_generator.generate_image_description(content_summary, topic)
            image_bytes = self.image_generator.generate_image(image_description)

            await processing_msg.edit_text(
                f"🔍 Processing: *{topic}*\n\n"
                "📊 Crawling sources... ✅\n"
                "🤖 Generating content... ✅\n"
                "🎨 Creating image... ✅\n"
                "📝 Preparing preview...",
                parse_mode="Markdown"
            )

            # Store in session
            self.user_sessions[user.id].update({
                "articles": articles,
                "content": content_summary,
                "image_bytes": image_bytes,
                "step": "preview"
            })

            # Send preview
            await self.send_content_preview(update, user.id)
            await processing_msg.delete()

        except Exception as e:
            logger.error(f"Error processing topic {topic}: {e}")
            await processing_msg.edit_text(
                f"❌ Sorry, there was an error processing your request: {str(e)}\n\n"
                "Please try again with a different topic."
            )
    
    async def send_content_preview(self, update: Update, user_id: int):
        """Send content preview to user"""
        session = self.user_sessions.get(user_id)
        if not session:
            return
        
        content = session["content"]
        image_bytes = session.get("image_bytes")
        
        # Prepare preview text
        preview_text = f"""
📝 *Content Preview*

*Short Post:*
{content.get('short_post', 'N/A')}

*Long Post:*
{content.get('long_post', 'N/A')}

*Hashtags:*
{' '.join([f"#{tag}" for tag in content.get('hashtags', [])])}

*Key Insights:*
{chr(10).join([f"• {insight}" for insight in content.get('key_insights', [])])}
        """
        
        # Create approval buttons
        keyboard = [
            [
                InlineKeyboardButton("✅ Approve", callback_data="approve_content"),
                InlineKeyboardButton("🔄 Regenerate", callback_data="regenerate_content")
            ],
            [InlineKeyboardButton("❌ Cancel", callback_data="cancel_operation")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # Send image if available
        if image_bytes:
            try:
                await update.effective_chat.send_photo(
                    photo=io.BytesIO(image_bytes),
                    caption=preview_text,
                    parse_mode="Markdown",
                    reply_markup=reply_markup
                )
            except Exception as e:
                logger.error(f"Error sending image: {e}")
                await update.effective_chat.send_message(
                    text=preview_text + "\n\n⚠️ Image generation failed, but content is ready!",
                    parse_mode="Markdown",
                    reply_markup=reply_markup
                )
        else:
            await update.effective_chat.send_message(
                text=preview_text + "\n\n⚠️ Image generation failed, but content is ready!",
                parse_mode="Markdown",
                reply_markup=reply_markup
            )
    
    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle button callbacks"""
        query = update.callback_query
        await query.answer()

        telegram_user = update.effective_user
        user = user_service.get_user_by_telegram_id(str(telegram_user.id))

        if not user and not query.data.startswith(("help", "dashboard", "add_funds", "connect")):
            await query.edit_message_text("❌ User not found. Please use /start to register.")
            return

        # Handle SaaS-specific callbacks
        if query.data == "dashboard":
            await self.show_dashboard_callback(query, user)
        elif query.data == "add_funds":
            await self.show_add_funds_callback(query, user)
        elif query.data.startswith("add_funds_"):
            amount = int(query.data.split("_")[2])
            await self.process_add_funds(query, user, amount)
        elif query.data == "connect_accounts":
            await self.show_connect_accounts_callback(query, user)
        elif query.data.startswith("connect_"):
            platform = query.data.split("_")[1]
            await self.handle_social_connect(query, user, platform)
        elif query.data == "transaction_history":
            await self.show_transaction_history(query, user)
        elif query.data == "help":
            await self.show_help_callback(query)

        # Handle existing content creation callbacks
        else:
            session = self.user_sessions.get(user.id if user else telegram_user.id)

            if not session and query.data not in ["help", "dashboard", "add_funds", "connect_accounts"]:
                await query.edit_message_text("❌ Session expired. Please start over with a new topic.")
                return

            if query.data == "approve_content":
                await self.show_platform_selection(query, user.id)

            elif query.data == "regenerate_content":
                await query.edit_message_text("🔄 Regenerating content...")
                # Regenerate content with same articles
                topic = session["topic"]
                articles = session["articles"]

                try:
                    new_content = self.ai_generator.generate_content_summary(articles, topic)
                    new_image_description = self.ai_generator.generate_image_description(new_content, topic)
                    new_image_bytes = self.image_generator.generate_image(new_image_description)

                    session.update({
                        "content": new_content,
                        "image_bytes": new_image_bytes
                    })

                    await self.send_content_preview(update, user.id)

                except Exception as e:
                    await query.edit_message_text(f"❌ Error regenerating content: {str(e)}")

            elif query.data == "cancel_operation":
                if user.id in self.user_sessions:
                    del self.user_sessions[user.id]
                await query.edit_message_text("❌ Operation cancelled.")

            elif query.data.startswith("platform_"):
                await self.handle_platform_selection(query, user.id)

            elif query.data == "post_to_selected":
                await self.post_to_selected_platforms(query, user.id)

    async def show_dashboard_callback(self, query, user):
        """Show dashboard via callback"""
        user_stats = user_service.get_user_stats(user.id)
        recent_transactions = payment_service.get_user_transactions(user.id, limit=5)
        connected_accounts = social_auth_service.get_user_social_accounts(user.id)

        dashboard_message = f"""
📊 **Your Dashboard**

👤 **Account Info:**
• Name: {user.first_name} {user.last_name or ''}
• Username: @{user.username or 'Not set'}
• Member Since: {user.registration_date.strftime('%B %d, %Y')}

💰 **Balance & Usage:**
• Current Balance: ₹{user.balance:.2f}
• Total Posts Created: {user_stats['total_posts']}
• Successful Posts: {user_stats['successful_posts']}
• Posts This Month: {user_stats['posts_this_month']}
• Total Spent: ₹{user_stats['total_spent']:.2f}

🔗 **Connected Accounts:**
{self._format_connected_accounts(connected_accounts)}

📈 **Recent Transactions:**
{self._format_recent_transactions(recent_transactions)}
        """

        keyboard = [
            [
                InlineKeyboardButton("💰 Add Funds", callback_data="add_funds"),
                InlineKeyboardButton("🔗 Connect Accounts", callback_data="connect_accounts")
            ],
            [
                InlineKeyboardButton("📜 Transaction History", callback_data="transaction_history"),
                InlineKeyboardButton("🔄 Refresh", callback_data="dashboard")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(dashboard_message, reply_markup=reply_markup, parse_mode="Markdown")

    async def show_add_funds_callback(self, query, user):
        """Show add funds via callback"""
        add_funds_message = f"""
💰 **Add Funds to Your Account**

Current Balance: ₹{user.balance:.2f}

Select an amount to add:
        """

        keyboard = [
            [
                InlineKeyboardButton("₹100", callback_data="add_funds_100"),
                InlineKeyboardButton("₹200", callback_data="add_funds_200"),
                InlineKeyboardButton("₹500", callback_data="add_funds_500")
            ],
            [
                InlineKeyboardButton("₹1000", callback_data="add_funds_1000"),
                InlineKeyboardButton("₹2000", callback_data="add_funds_2000"),
                InlineKeyboardButton("₹5000", callback_data="add_funds_5000")
            ],
            [InlineKeyboardButton("🔙 Back to Dashboard", callback_data="dashboard")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(add_funds_message, reply_markup=reply_markup, parse_mode="Markdown")

    async def process_add_funds(self, query, user, amount: int):
        """Process add funds request"""
        # For demo purposes, we'll add funds directly
        # In production, this would integrate with a payment gateway
        result = payment_service.add_funds(user.id, amount, "manual")

        if result["success"]:
            success_message = f"""
✅ **Funds Added Successfully!**

Amount Added: ₹{amount}
New Balance: ₹{result['new_balance']:.2f}
Transaction ID: {result['transaction_id'][:8]}...

You can now create posts! Each post costs ₹{settings.post_price_inr}.
            """

            keyboard = [
                [InlineKeyboardButton("📊 Dashboard", callback_data="dashboard")],
                [InlineKeyboardButton("🚀 Create Post", callback_data="create_post")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(success_message, reply_markup=reply_markup, parse_mode="Markdown")
        else:
            error_message = f"❌ Failed to add funds: {result.get('error', 'Unknown error')}"
            await query.edit_message_text(error_message)

    async def show_connect_accounts_callback(self, query, user):
        """Show connect accounts via callback"""
        connected_accounts = social_auth_service.get_user_social_accounts(user.id)

        connect_message = f"""
🔗 **Connect Social Media Accounts**

Connect your social media accounts to post directly:

**Currently Connected:**
{self._format_connected_accounts(connected_accounts)}

Select a platform to connect:
        """

        keyboard = [
            [
                InlineKeyboardButton("💼 LinkedIn", callback_data="connect_linkedin"),
                InlineKeyboardButton("📘 Facebook", callback_data="connect_facebook")
            ],
            [
                InlineKeyboardButton("📷 Instagram", callback_data="connect_instagram")
            ],
            [InlineKeyboardButton("🔙 Back to Dashboard", callback_data="dashboard")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(connect_message, reply_markup=reply_markup, parse_mode="Markdown")

    async def handle_social_connect(self, query, user, platform: str):
        """Handle social media connection"""
        if platform == "linkedin":
            auth_url = social_auth_service.get_linkedin_auth_url(user.id)
        elif platform == "facebook":
            auth_url = social_auth_service.get_facebook_auth_url(user.id)
        elif platform == "instagram":
            auth_url = social_auth_service.get_instagram_auth_url(user.id)
        else:
            await query.edit_message_text("❌ Unsupported platform")
            return

        connect_message = f"""
🔗 **Connect {platform.title()} Account**

Click the button below to authorize K-Poster to post on your behalf:

⚠️ **Note:** You'll be redirected to {platform.title()}'s authorization page. After granting permission, you'll be redirected back.
        """

        keyboard = [
            [InlineKeyboardButton(f"🔗 Connect {platform.title()}", url=auth_url)],
            [InlineKeyboardButton("🔙 Back", callback_data="connect_accounts")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(connect_message, reply_markup=reply_markup, parse_mode="Markdown")

    async def show_transaction_history(self, query, user):
        """Show transaction history"""
        transactions = payment_service.get_user_transactions(user.id, limit=10)

        if not transactions:
            history_message = "📜 **Transaction History**\n\nNo transactions found."
        else:
            history_message = "📜 **Transaction History**\n\n"
            for txn in transactions:
                type_emoji = {"payment": "💳", "deduction": "💸", "refund": "💰"}.get(txn['type'], "💱")
                status_emoji = {"completed": "✅", "pending": "⏳", "failed": "❌"}.get(txn['status'], "❓")
                date_str = txn['created_at'].strftime('%m/%d %H:%M')
                history_message += f"{type_emoji} ₹{txn['amount']:.2f} - {txn['description'][:40]}... {status_emoji}\n"
                history_message += f"   {date_str} | ID: {txn['transaction_id'][:8]}...\n\n"

        keyboard = [[InlineKeyboardButton("🔙 Back to Dashboard", callback_data="dashboard")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(history_message, reply_markup=reply_markup, parse_mode="Markdown")

    async def show_help_callback(self, query):
        """Show help via callback"""
        help_message = """
📖 **K-Poster SaaS Bot Help**

🎯 **How it works:**
1. Add funds to your account (₹20 per post)
2. Send me any topic you want to post about
3. I'll research, generate content & images
4. Review and approve the content
5. Select platforms to post to
6. Done! Your content is posted automatically

💰 **Pricing:**
• ₹20 per post (pay-as-you-go)
• Subscription plans coming soon!

🔗 **Supported Platforms:**
• LinkedIn (Professional posts)
• Facebook (Page posts)
• Instagram (Coming soon)

📱 **Commands:**
/start - Register & view welcome
/dashboard - View account details
/add_funds - Add money to account
/connect - Connect social accounts
/help - Show this help

💡 **Tips:**
• Connect your social accounts first
• Be specific with your topics
• Review content before posting
• Check your balance regularly
        """

        keyboard = [[InlineKeyboardButton("🔙 Back", callback_data="dashboard")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(help_message, reply_markup=reply_markup, parse_mode="Markdown")

    async def show_platform_selection(self, query, user_id: int):
        """Show platform selection buttons"""
        session = self.user_sessions.get(user_id)
        if not session:
            return
        
        session["selected_platforms"] = []
        session["step"] = "platform_selection"
        
        keyboard = [
            [
                InlineKeyboardButton("📘 LinkedIn", callback_data="platform_linkedin"),
                InlineKeyboardButton("📘 Facebook", callback_data="platform_facebook")
            ],
            [
                InlineKeyboardButton("📷 Instagram", callback_data="platform_instagram")
            ],
            [
                InlineKeyboardButton("✅ Post to Selected", callback_data="post_to_selected"),
                InlineKeyboardButton("❌ Cancel", callback_data="cancel_operation")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            "📱 *Select platforms to post to:*\n\n"
            "Click on the platforms where you want to post your content.\n"
            "Then click 'Post to Selected' when ready.",
            parse_mode="Markdown",
            reply_markup=reply_markup
        )
    
    async def handle_platform_selection(self, query, user_id: int):
        """Handle platform selection"""
        session = self.user_sessions.get(user_id)
        if not session:
            return
        
        platform = query.data.replace("platform_", "")
        selected_platforms = session.get("selected_platforms", [])
        
        if platform in selected_platforms:
            selected_platforms.remove(platform)
        else:
            selected_platforms.append(platform)
        
        session["selected_platforms"] = selected_platforms
        
        # Update button text to show selection
        platform_emojis = {
            "linkedin": "📘",
            "facebook": "📘", 
            "instagram": "📷"
        }
        
        keyboard = []
        for p in ["linkedin", "facebook"]:
            emoji = platform_emojis[p]
            text = f"{emoji} {p.title()}"
            if p in selected_platforms:
                text += " ✅"
            keyboard.append(InlineKeyboardButton(text, callback_data=f"platform_{p}"))
        
        keyboard = [keyboard]  # First row
        
        # Instagram row
        instagram_text = f"{platform_emojis['instagram']} Instagram"
        if "instagram" in selected_platforms:
            instagram_text += " ✅"
        keyboard.append([InlineKeyboardButton(instagram_text, callback_data="platform_instagram")])
        
        # Action buttons
        keyboard.append([
            InlineKeyboardButton("✅ Post to Selected", callback_data="post_to_selected"),
            InlineKeyboardButton("❌ Cancel", callback_data="cancel_operation")
        ])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        selected_text = ", ".join(selected_platforms) if selected_platforms else "None"
        
        await query.edit_message_text(
            f"📱 *Select platforms to post to:*\n\n"
            f"*Selected:* {selected_text}\n\n"
            "Click on platforms to toggle selection.\n"
            "Then click 'Post to Selected' when ready.",
            parse_mode="Markdown",
            reply_markup=reply_markup
        )
    
    async def post_to_selected_platforms(self, query, user_id: int):
        """Post content to selected platforms with payment processing"""
        session = self.user_sessions.get(user_id)
        if not session:
            return

        selected_platforms = session.get("selected_platforms", [])
        if not selected_platforms:
            await query.edit_message_text("❌ Please select at least one platform.")
            return

        # Process payment for the post
        can_create_result = session.get("can_create_result", {})

        # Only charge if not using subscription
        if can_create_result.get("reason") != "subscription":
            payment_result = payment_service.charge_for_post(user_id, settings.post_price_inr, f"Post creation: {session['topic']}")

            if not payment_result["success"]:
                await query.edit_message_text(
                    f"❌ Payment failed: {payment_result.get('message', 'Unknown error')}\n\n"
                    "Please add funds to your account and try again."
                )
                return

        content = session["content"]
        image_bytes = session.get("image_bytes")

        # Create post record in database
        with get_db_context() as db_session:
            post_record = Post(
                user_id=user_id,
                topic=session["topic"],
                short_post=content.get("short_post"),
                long_post=content.get("long_post"),
                hashtags=content.get("hashtags"),
                key_insights=content.get("key_insights"),
                image_description=session.get("image_description"),
                status="approved",
                platforms_posted=selected_platforms,
                cost=settings.post_price_inr if can_create_result.get("reason") != "subscription" else 0.0
            )
            db_session.add(post_record)
            db_session.commit()
            db_session.refresh(post_record)

            post_id = post_record.id

        await query.edit_message_text(
            f"📤 Posting to {', '.join(selected_platforms)}...\n\n"
            "⏳ Please wait..."
        )

        try:
            results = {}
            for platform in selected_platforms:
                # Enhance content for specific platform
                platform_content = self.ai_generator.enhance_content_for_platform(content, platform)

                # Resize image for platform if needed
                platform_image = None
                if image_bytes:
                    platform_image = self.image_generator.resize_image_for_platform(image_bytes, platform)

                # Post to platform
                result = self.social_poster.post_to_platform(platform, platform_content, platform_image)
                results[platform] = result

            # Update post record with results
            with get_db_context() as db_session:
                post_record = db_session.query(Post).filter(Post.id == post_id).first()
                if post_record:
                    post_record.posting_results = results
                    post_record.status = "posted" if any(r.get("success") for r in results.values()) else "failed"
                    if post_record.status == "posted":
                        post_record.posted_at = datetime.utcnow()
                    db_session.commit()

            # Update user stats
            user_service.update_user_info(user_id, total_posts=user_service.get_user_stats(user_id)["total_posts"] + 1)

            # Send results
            await self.send_posting_results(query, results, post_id)

            # Clean up session
            if user_id in self.user_sessions:
                del self.user_sessions[user_id]

        except Exception as e:
            logger.error(f"Error posting to platforms: {e}")

            # Update post record as failed
            with get_db_context() as db_session:
                post_record = db_session.query(Post).filter(Post.id == post_id).first()
                if post_record:
                    post_record.status = "failed"
                    post_record.posting_results = {"error": str(e)}
                    db_session.commit()

            await query.edit_message_text(f"❌ Error posting to platforms: {str(e)}")
    
    async def send_posting_results(self, query, results: Dict, post_id: int = None):
        """Send posting results to user"""
        result_text = "📊 *Posting Results:*\n\n"

        successful_platforms = []
        failed_platforms = []

        for platform, result in results.items():
            if result.get("success"):
                result_text += f"✅ {platform.title()}: Posted successfully\n"
                successful_platforms.append(platform)
            else:
                error = result.get("error", "Unknown error")
                result_text += f"❌ {platform.title()}: Failed - {error}\n"
                failed_platforms.append(platform)

        if post_id:
            result_text += f"\n📝 Post ID: {post_id}\n"

        if successful_platforms:
            result_text += f"\n🎉 Successfully posted to: {', '.join(successful_platforms)}\n"

        if failed_platforms:
            result_text += f"\n⚠️ Failed to post to: {', '.join(failed_platforms)}\n"

        result_text += "\n💡 Send me another topic to create more posts!"

        # Add action buttons
        keyboard = [
            [
                InlineKeyboardButton("📊 Dashboard", callback_data="dashboard"),
                InlineKeyboardButton("🚀 Create Another Post", callback_data="create_post")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(result_text, reply_markup=reply_markup, parse_mode="Markdown")
    
    def run(self):
        """Run the Telegram bot"""
        logger.info("Starting K-Poster SaaS Telegram bot...")

        # Create application
        application = Application.builder().token(self.token).build()

        # Add command handlers
        application.add_handler(CommandHandler("start", self.start_command))
        application.add_handler(CommandHandler("help", self.help_command))
        application.add_handler(CommandHandler("cancel", self.cancel_command))
        application.add_handler(CommandHandler("dashboard", self.dashboard_command))
        application.add_handler(CommandHandler("add_funds", self.add_funds_command))
        application.add_handler(CommandHandler("connect", self.connect_accounts_command))

        # Add callback and message handlers
        application.add_handler(CallbackQueryHandler(self.handle_callback_query))
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_topic_input))

        # Run the bot
        logger.info("K-Poster SaaS Bot is running...")
        application.run_polling()
