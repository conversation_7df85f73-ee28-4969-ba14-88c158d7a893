from sqlalchemy.orm import Session
from sqlalchemy import and_
from models.database import User, Transaction, Post, Subscription
from database.connection import get_db_context
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class UserService:
    def __init__(self):
        pass
    
    def get_or_create_user(self, telegram_user_id: str, user_data: Dict[str, Any]) -> User:
        """Get existing user or create new one"""
        with get_db_context() as session:
            # Try to find existing user
            user = session.query(User).filter(
                User.telegram_user_id == telegram_user_id
            ).first()
            
            if user:
                # Update last login
                user.last_login = datetime.utcnow()
                session.commit()
                return user
            
            # Create new user
            user = User(
                telegram_user_id=telegram_user_id,
                username=user_data.get('username'),
                first_name=user_data.get('first_name'),
                last_name=user_data.get('last_name'),
                registration_date=datetime.utcnow(),
                last_login=datetime.utcnow(),
                balance=0.0,
                total_posts=0,
                subscription_type="free"
            )
            
            session.add(user)
            session.commit()
            session.refresh(user)
            
            # Create default subscription
            self._create_default_subscription(session, user.id)
            
            logger.info(f"Created new user: {telegram_user_id}")
            return user
    
    def _create_default_subscription(self, session: Session, user_id: int):
        """Create default free subscription for new user"""
        subscription = Subscription(
            user_id=user_id,
            plan_name="free",
            plan_price=0.0,
            billing_cycle="monthly",
            status="active",
            starts_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(days=365),  # Free plan for 1 year
            monthly_post_limit=5,
            posts_used_this_month=0
        )
        session.add(subscription)
        session.commit()
    
    def get_user_by_telegram_id(self, telegram_user_id: str) -> Optional[User]:
        """Get user by telegram ID"""
        with get_db_context() as session:
            return session.query(User).filter(
                User.telegram_user_id == telegram_user_id
            ).first()
    
    def get_user_balance(self, user_id: int) -> float:
        """Get user's current balance"""
        with get_db_context() as session:
            user = session.query(User).filter(User.id == user_id).first()
            return user.balance if user else 0.0
    
    def add_balance(self, user_id: int, amount: float, transaction_id: Optional[int] = None) -> bool:
        """Add balance to user account"""
        try:
            with get_db_context() as session:
                user = session.query(User).filter(User.id == user_id).first()
                if not user:
                    return False
                
                user.balance += amount
                session.commit()
                
                logger.info(f"Added {amount} to user {user_id} balance. New balance: {user.balance}")
                return True
        except Exception as e:
            logger.error(f"Error adding balance to user {user_id}: {e}")
            return False
    
    def deduct_balance(self, user_id: int, amount: float, description: str = "") -> bool:
        """Deduct balance from user account"""
        try:
            with get_db_context() as session:
                user = session.query(User).filter(User.id == user_id).first()
                if not user or user.balance < amount:
                    return False
                
                user.balance -= amount
                session.commit()
                
                logger.info(f"Deducted {amount} from user {user_id} balance. New balance: {user.balance}")
                return True
        except Exception as e:
            logger.error(f"Error deducting balance from user {user_id}: {e}")
            return False
    
    def can_create_post(self, user_id: int, cost: float = 20.0) -> Dict[str, Any]:
        """Check if user can create a post (has sufficient balance or subscription)"""
        with get_db_context() as session:
            user = session.query(User).filter(User.id == user_id).first()
            if not user:
                return {"can_create": False, "reason": "User not found"}
            
            # Check subscription limits
            subscription = session.query(Subscription).filter(
                and_(
                    Subscription.user_id == user_id,
                    Subscription.status == "active",
                    Subscription.expires_at > datetime.utcnow()
                )
            ).first()
            
            if subscription:
                # Check monthly limit for subscription users
                current_month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                posts_this_month = session.query(Post).filter(
                    and_(
                        Post.user_id == user_id,
                        Post.created_at >= current_month_start,
                        Post.status.in_(["approved", "posted"])
                    )
                ).count()
                
                if posts_this_month < subscription.monthly_post_limit:
                    return {"can_create": True, "reason": "subscription", "remaining_posts": subscription.monthly_post_limit - posts_this_month}
            
            # Check balance for pay-per-post
            if user.balance >= cost:
                return {"can_create": True, "reason": "balance", "balance": user.balance}
            
            return {
                "can_create": False, 
                "reason": "insufficient_funds", 
                "balance": user.balance,
                "required": cost
            }
    
    def get_user_stats(self, user_id: int) -> Dict[str, Any]:
        """Get user statistics"""
        with get_db_context() as session:
            user = session.query(User).filter(User.id == user_id).first()
            if not user:
                return {}
            
            # Get subscription info
            subscription = session.query(Subscription).filter(
                and_(
                    Subscription.user_id == user_id,
                    Subscription.status == "active"
                )
            ).first()
            
            # Get posts count
            total_posts = session.query(Post).filter(Post.user_id == user_id).count()
            successful_posts = session.query(Post).filter(
                and_(Post.user_id == user_id, Post.status == "posted")
            ).count()
            
            # Get this month's posts
            current_month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            posts_this_month = session.query(Post).filter(
                and_(
                    Post.user_id == user_id,
                    Post.created_at >= current_month_start
                )
            ).count()
            
            # Get total spent
            total_spent = session.query(Transaction).filter(
                and_(
                    Transaction.user_id == user_id,
                    Transaction.transaction_type == "payment",
                    Transaction.status == "completed"
                )
            ).with_entities(Transaction.amount).all()
            
            total_spent_amount = sum([t.amount for t in total_spent]) if total_spent else 0.0
            
            return {
                "user_id": user.id,
                "telegram_user_id": user.telegram_user_id,
                "username": user.username,
                "registration_date": user.registration_date,
                "balance": user.balance,
                "total_posts": total_posts,
                "successful_posts": successful_posts,
                "posts_this_month": posts_this_month,
                "total_spent": total_spent_amount,
                "subscription": {
                    "plan_name": subscription.plan_name if subscription else "none",
                    "monthly_limit": subscription.monthly_post_limit if subscription else 0,
                    "expires_at": subscription.expires_at if subscription else None
                } if subscription else None
            }
    
    def update_user_info(self, user_id: int, **kwargs) -> bool:
        """Update user information"""
        try:
            with get_db_context() as session:
                user = session.query(User).filter(User.id == user_id).first()
                if not user:
                    return False
                
                for key, value in kwargs.items():
                    if hasattr(user, key):
                        setattr(user, key, value)
                
                session.commit()
                return True
        except Exception as e:
            logger.error(f"Error updating user {user_id}: {e}")
            return False

# Global instance
user_service = UserService()
