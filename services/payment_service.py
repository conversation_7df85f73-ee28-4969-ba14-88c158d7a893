from sqlalchemy.orm import Session
from models.database import Transaction, User
from database.connection import get_db_context
from services.user_service import user_service
from datetime import datetime
from typing import Dict, Any, Optional
import uuid
import logging

logger = logging.getLogger(__name__)

class PaymentService:
    def __init__(self):
        pass
    
    def create_transaction(self, user_id: int, amount: float, transaction_type: str, 
                          description: str = "", payment_gateway: str = "manual") -> Transaction:
        """Create a new transaction record"""
        with get_db_context() as session:
            transaction = Transaction(
                transaction_id=str(uuid.uuid4()),
                user_id=user_id,
                amount=amount,
                currency="INR",
                transaction_type=transaction_type,
                status="pending",
                payment_gateway=payment_gateway,
                description=description,
                created_at=datetime.utcnow()
            )
            
            session.add(transaction)
            session.commit()
            session.refresh(transaction)
            
            logger.info(f"Created transaction {transaction.transaction_id} for user {user_id}")
            return transaction
    
    def complete_transaction(self, transaction_id: str, gateway_transaction_id: str = None, 
                           gateway_response: Dict = None) -> bool:
        """Mark transaction as completed and update user balance"""
        try:
            with get_db_context() as session:
                transaction = session.query(Transaction).filter(
                    Transaction.transaction_id == transaction_id
                ).first()
                
                if not transaction:
                    logger.error(f"Transaction {transaction_id} not found")
                    return False
                
                if transaction.status == "completed":
                    logger.warning(f"Transaction {transaction_id} already completed")
                    return True
                
                # Update transaction
                transaction.status = "completed"
                transaction.gateway_transaction_id = gateway_transaction_id
                transaction.gateway_response = gateway_response
                transaction.updated_at = datetime.utcnow()
                
                # Update user balance for payment transactions
                if transaction.transaction_type == "payment":
                    user_service.add_balance(transaction.user_id, transaction.amount, transaction.id)
                
                session.commit()
                
                logger.info(f"Completed transaction {transaction_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error completing transaction {transaction_id}: {e}")
            return False
    
    def fail_transaction(self, transaction_id: str, error_message: str = "") -> bool:
        """Mark transaction as failed"""
        try:
            with get_db_context() as session:
                transaction = session.query(Transaction).filter(
                    Transaction.transaction_id == transaction_id
                ).first()
                
                if not transaction:
                    return False
                
                transaction.status = "failed"
                transaction.description = f"{transaction.description} | Error: {error_message}"
                transaction.updated_at = datetime.utcnow()
                
                session.commit()
                
                logger.info(f"Failed transaction {transaction_id}: {error_message}")
                return True
                
        except Exception as e:
            logger.error(f"Error failing transaction {transaction_id}: {e}")
            return False
    
    def charge_for_post(self, user_id: int, post_cost: float = 20.0, description: str = "Post creation") -> Dict[str, Any]:
        """Charge user for creating a post"""
        try:
            # Check if user can afford the post
            can_create = user_service.can_create_post(user_id, post_cost)
            
            if not can_create["can_create"]:
                return {
                    "success": False,
                    "reason": can_create["reason"],
                    "message": self._get_insufficient_funds_message(can_create)
                }
            
            # If user has subscription with remaining posts, don't charge
            if can_create["reason"] == "subscription":
                return {
                    "success": True,
                    "charged": False,
                    "reason": "subscription",
                    "remaining_posts": can_create["remaining_posts"]
                }
            
            # Deduct from balance
            if user_service.deduct_balance(user_id, post_cost, description):
                # Create transaction record
                transaction = self.create_transaction(
                    user_id=user_id,
                    amount=post_cost,
                    transaction_type="deduction",
                    description=description
                )
                
                # Mark as completed immediately for balance deductions
                self.complete_transaction(transaction.transaction_id)
                
                return {
                    "success": True,
                    "charged": True,
                    "amount": post_cost,
                    "transaction_id": transaction.transaction_id,
                    "remaining_balance": user_service.get_user_balance(user_id)
                }
            else:
                return {
                    "success": False,
                    "reason": "deduction_failed",
                    "message": "Failed to deduct amount from balance"
                }
                
        except Exception as e:
            logger.error(f"Error charging user {user_id} for post: {e}")
            return {
                "success": False,
                "reason": "error",
                "message": f"Payment processing error: {str(e)}"
            }
    
    def _get_insufficient_funds_message(self, can_create_result: Dict) -> str:
        """Generate user-friendly message for insufficient funds"""
        if can_create_result["reason"] == "insufficient_funds":
            balance = can_create_result.get("balance", 0)
            required = can_create_result.get("required", 20)
            needed = required - balance
            return f"Insufficient balance. You have ₹{balance:.2f}, need ₹{required:.2f}. Please add ₹{needed:.2f} to your account."
        elif can_create_result["reason"] == "User not found":
            return "User account not found. Please register first."
        else:
            return "Cannot create post at this time."
    
    def add_funds(self, user_id: int, amount: float, payment_method: str = "manual") -> Dict[str, Any]:
        """Add funds to user account"""
        try:
            # Create payment transaction
            transaction = self.create_transaction(
                user_id=user_id,
                amount=amount,
                transaction_type="payment",
                description=f"Add funds via {payment_method}",
                payment_gateway=payment_method
            )
            
            # For manual/admin additions, complete immediately
            if payment_method == "manual":
                self.complete_transaction(transaction.transaction_id)
                return {
                    "success": True,
                    "transaction_id": transaction.transaction_id,
                    "amount": amount,
                    "new_balance": user_service.get_user_balance(user_id)
                }
            
            # For other payment methods, return transaction for processing
            return {
                "success": True,
                "transaction_id": transaction.transaction_id,
                "amount": amount,
                "status": "pending",
                "payment_url": f"/payment/{transaction.transaction_id}"  # Placeholder for payment gateway URL
            }
            
        except Exception as e:
            logger.error(f"Error adding funds for user {user_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_user_transactions(self, user_id: int, limit: int = 10) -> list:
        """Get user's transaction history"""
        with get_db_context() as session:
            transactions = session.query(Transaction).filter(
                Transaction.user_id == user_id
            ).order_by(Transaction.created_at.desc()).limit(limit).all()
            
            return [
                {
                    "id": t.id,
                    "transaction_id": t.transaction_id,
                    "amount": t.amount,
                    "type": t.transaction_type,
                    "status": t.status,
                    "description": t.description,
                    "created_at": t.created_at,
                    "gateway": t.payment_gateway
                }
                for t in transactions
            ]
    
    def get_transaction_by_id(self, transaction_id: str) -> Optional[Transaction]:
        """Get transaction by ID"""
        with get_db_context() as session:
            return session.query(Transaction).filter(
                Transaction.transaction_id == transaction_id
            ).first()

# Global instance
payment_service = PaymentService()
