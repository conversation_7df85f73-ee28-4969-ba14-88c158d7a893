from sqlalchemy.orm import Session
from models.database import SocialMediaAccount, User
from database.connection import get_db_context
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import requests
import base64
import json
import logging
from cryptography.fernet import Fernet
from config import settings

logger = logging.getLogger(__name__)

class SocialAuthService:
    def __init__(self):
        # Initialize encryption for storing tokens securely
        self.cipher_suite = Fernet(settings.encryption_key.encode()[:32].ljust(32, b'0'))
    
    def _encrypt_token(self, token: str) -> str:
        """Encrypt token for secure storage"""
        if not token:
            return ""
        return self.cipher_suite.encrypt(token.encode()).decode()
    
    def _decrypt_token(self, encrypted_token: str) -> str:
        """Decrypt token for use"""
        if not encrypted_token:
            return ""
        try:
            return self.cipher_suite.decrypt(encrypted_token.encode()).decode()
        except Exception as e:
            logger.error(f"Error decrypting token: {e}")
            return ""
    
    def get_linkedin_auth_url(self, user_id: int) -> str:
        """Generate LinkedIn OAuth URL"""
        state = f"linkedin_{user_id}_{datetime.utcnow().timestamp()}"
        
        params = {
            "response_type": "code",
            "client_id": settings.linkedin_client_id,
            "redirect_uri": settings.linkedin_redirect_uri,
            "state": state,
            "scope": "r_liteprofile r_emailaddress w_member_social"
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"https://www.linkedin.com/oauth/v2/authorization?{query_string}"
    
    def get_facebook_auth_url(self, user_id: int) -> str:
        """Generate Facebook OAuth URL"""
        state = f"facebook_{user_id}_{datetime.utcnow().timestamp()}"
        
        params = {
            "client_id": settings.facebook_app_id,
            "redirect_uri": settings.facebook_redirect_uri,
            "state": state,
            "scope": "pages_manage_posts,pages_read_engagement,publish_to_groups",
            "response_type": "code"
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"https://www.facebook.com/v18.0/dialog/oauth?{query_string}"
    
    def get_instagram_auth_url(self, user_id: int) -> str:
        """Generate Instagram OAuth URL"""
        state = f"instagram_{user_id}_{datetime.utcnow().timestamp()}"
        
        params = {
            "client_id": settings.instagram_client_id,
            "redirect_uri": settings.instagram_redirect_uri,
            "state": state,
            "scope": "user_profile,user_media",
            "response_type": "code"
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"https://api.instagram.com/oauth/authorize?{query_string}"
    
    def handle_linkedin_callback(self, code: str, state: str) -> Dict[str, Any]:
        """Handle LinkedIn OAuth callback"""
        try:
            # Extract user_id from state
            user_id = int(state.split("_")[1])
            
            # Exchange code for access token
            token_data = {
                "grant_type": "authorization_code",
                "code": code,
                "redirect_uri": settings.linkedin_redirect_uri,
                "client_id": settings.linkedin_client_id,
                "client_secret": settings.linkedin_client_secret
            }
            
            response = requests.post(
                "https://www.linkedin.com/oauth/v2/accessToken",
                data=token_data
            )
            
            if response.status_code != 200:
                return {"success": False, "error": "Failed to get access token"}
            
            token_info = response.json()
            access_token = token_info.get("access_token")
            expires_in = token_info.get("expires_in", 3600)
            
            # Get user profile
            profile_response = requests.get(
                "https://api.linkedin.com/v2/people/~:(id,firstName,lastName,profilePicture(displayImage~:playableStreams))",
                headers={"Authorization": f"Bearer {access_token}"}
            )
            
            if profile_response.status_code != 200:
                return {"success": False, "error": "Failed to get user profile"}
            
            profile_data = profile_response.json()
            
            # Save account
            account = self.save_social_account(
                user_id=user_id,
                platform="linkedin",
                platform_user_id=profile_data.get("id"),
                username=f"{profile_data.get('firstName', {}).get('localized', {}).get('en_US', '')} {profile_data.get('lastName', {}).get('localized', {}).get('en_US', '')}",
                display_name=f"{profile_data.get('firstName', {}).get('localized', {}).get('en_US', '')} {profile_data.get('lastName', {}).get('localized', {}).get('en_US', '')}",
                access_token=access_token,
                token_expires_at=datetime.utcnow() + timedelta(seconds=expires_in),
                account_data=profile_data
            )
            
            return {"success": True, "account_id": account.id}
            
        except Exception as e:
            logger.error(f"LinkedIn callback error: {e}")
            return {"success": False, "error": str(e)}
    
    def handle_facebook_callback(self, code: str, state: str) -> Dict[str, Any]:
        """Handle Facebook OAuth callback"""
        try:
            user_id = int(state.split("_")[1])
            
            # Exchange code for access token
            token_url = f"https://graph.facebook.com/v18.0/oauth/access_token"
            token_params = {
                "client_id": settings.facebook_app_id,
                "client_secret": settings.facebook_app_secret,
                "redirect_uri": settings.facebook_redirect_uri,
                "code": code
            }
            
            response = requests.get(token_url, params=token_params)
            
            if response.status_code != 200:
                return {"success": False, "error": "Failed to get access token"}
            
            token_info = response.json()
            access_token = token_info.get("access_token")
            
            # Get user profile
            profile_response = requests.get(
                f"https://graph.facebook.com/me?fields=id,name,email&access_token={access_token}"
            )
            
            if profile_response.status_code != 200:
                return {"success": False, "error": "Failed to get user profile"}
            
            profile_data = profile_response.json()
            
            # Save account
            account = self.save_social_account(
                user_id=user_id,
                platform="facebook",
                platform_user_id=profile_data.get("id"),
                username=profile_data.get("name"),
                display_name=profile_data.get("name"),
                access_token=access_token,
                account_data=profile_data
            )
            
            return {"success": True, "account_id": account.id}
            
        except Exception as e:
            logger.error(f"Facebook callback error: {e}")
            return {"success": False, "error": str(e)}
    
    def save_social_account(self, user_id: int, platform: str, platform_user_id: str,
                           username: str, display_name: str, access_token: str,
                           refresh_token: str = None, token_expires_at: datetime = None,
                           account_data: Dict = None) -> SocialMediaAccount:
        """Save or update social media account"""
        with get_db_context() as session:
            # Check if account already exists
            existing_account = session.query(SocialMediaAccount).filter(
                SocialMediaAccount.user_id == user_id,
                SocialMediaAccount.platform == platform
            ).first()
            
            if existing_account:
                # Update existing account
                existing_account.platform_user_id = platform_user_id
                existing_account.username = username
                existing_account.display_name = display_name
                existing_account.access_token = self._encrypt_token(access_token)
                existing_account.refresh_token = self._encrypt_token(refresh_token) if refresh_token else None
                existing_account.token_expires_at = token_expires_at
                existing_account.account_data = account_data
                existing_account.is_active = True
                existing_account.is_verified = True
                existing_account.updated_at = datetime.utcnow()
                
                session.commit()
                return existing_account
            else:
                # Create new account
                account = SocialMediaAccount(
                    user_id=user_id,
                    platform=platform,
                    platform_user_id=platform_user_id,
                    username=username,
                    display_name=display_name,
                    access_token=self._encrypt_token(access_token),
                    refresh_token=self._encrypt_token(refresh_token) if refresh_token else None,
                    token_expires_at=token_expires_at,
                    is_active=True,
                    is_verified=True,
                    account_data=account_data,
                    created_at=datetime.utcnow()
                )
                
                session.add(account)
                session.commit()
                session.refresh(account)
                return account
    
    def get_user_social_accounts(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all social media accounts for a user"""
        with get_db_context() as session:
            accounts = session.query(SocialMediaAccount).filter(
                SocialMediaAccount.user_id == user_id,
                SocialMediaAccount.is_active == True
            ).all()
            
            return [
                {
                    "id": account.id,
                    "platform": account.platform,
                    "username": account.username,
                    "display_name": account.display_name,
                    "is_verified": account.is_verified,
                    "last_used": account.last_used,
                    "created_at": account.created_at
                }
                for account in accounts
            ]
    
    def get_account_token(self, account_id: int) -> Optional[str]:
        """Get decrypted access token for an account"""
        with get_db_context() as session:
            account = session.query(SocialMediaAccount).filter(
                SocialMediaAccount.id == account_id,
                SocialMediaAccount.is_active == True
            ).first()
            
            if not account:
                return None
            
            return self._decrypt_token(account.access_token)
    
    def disconnect_account(self, user_id: int, platform: str) -> bool:
        """Disconnect a social media account"""
        try:
            with get_db_context() as session:
                account = session.query(SocialMediaAccount).filter(
                    SocialMediaAccount.user_id == user_id,
                    SocialMediaAccount.platform == platform
                ).first()
                
                if account:
                    account.is_active = False
                    account.updated_at = datetime.utcnow()
                    session.commit()
                    return True
                
                return False
        except Exception as e:
            logger.error(f"Error disconnecting {platform} account for user {user_id}: {e}")
            return False

# Global instance
social_auth_service = SocialAuthService()
