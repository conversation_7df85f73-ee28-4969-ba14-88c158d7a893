#!/usr/bin/env python3
"""
Setup script for Vercel deployment
"""

import os
import requests
import json
from config import settings

def setup_telegram_webhook(vercel_url: str):
    """Setup Telegram webhook to point to Vercel deployment"""
    try:
        webhook_url = f"{vercel_url}/api/webhook"
        telegram_api_url = f"https://api.telegram.org/bot{settings.telegram_bot_token}/setWebhook"
        
        payload = {
            "url": webhook_url,
            "allowed_updates": ["message", "callback_query"]
        }
        
        response = requests.post(telegram_api_url, json=payload)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("ok"):
                print(f"✅ Webhook set successfully to: {webhook_url}")
                return True
            else:
                print(f"❌ Telegram API error: {result.get('description')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error setting webhook: {e}")
        return False

def get_webhook_info():
    """Get current webhook information"""
    try:
        telegram_api_url = f"https://api.telegram.org/bot{settings.telegram_bot_token}/getWebhookInfo"
        
        response = requests.get(telegram_api_url)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("ok"):
                webhook_info = result.get("result", {})
                print("📋 Current Webhook Info:")
                print(f"   URL: {webhook_info.get('url', 'Not set')}")
                print(f"   Has Custom Certificate: {webhook_info.get('has_custom_certificate', False)}")
                print(f"   Pending Update Count: {webhook_info.get('pending_update_count', 0)}")
                print(f"   Last Error Date: {webhook_info.get('last_error_date', 'None')}")
                print(f"   Last Error Message: {webhook_info.get('last_error_message', 'None')}")
                return webhook_info
            else:
                print(f"❌ Telegram API error: {result.get('description')}")
                return None
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error getting webhook info: {e}")
        return None

def delete_webhook():
    """Delete current webhook"""
    try:
        telegram_api_url = f"https://api.telegram.org/bot{settings.telegram_bot_token}/deleteWebhook"
        
        response = requests.post(telegram_api_url)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("ok"):
                print("✅ Webhook deleted successfully")
                return True
            else:
                print(f"❌ Telegram API error: {result.get('description')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error deleting webhook: {e}")
        return False

def test_vercel_deployment(vercel_url: str):
    """Test the Vercel deployment"""
    try:
        # Test health endpoint
        health_url = f"{vercel_url}/api/health"
        
        print(f"🧪 Testing deployment at: {health_url}")
        
        response = requests.get(health_url, timeout=30)
        
        if response.status_code == 200:
            health_data = response.json()
            print("✅ Health check passed")
            print(f"   Status: {health_data.get('status')}")
            print(f"   Service: {health_data.get('service')}")
            print(f"   Environment: {health_data.get('environment')}")
            
            checks = health_data.get('checks', {})
            for check_name, check_result in checks.items():
                status = "✅" if check_result else "❌"
                print(f"   {check_name}: {status}")
            
            return health_data.get('status') == 'healthy'
        else:
            print(f"❌ Health check failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing deployment: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 K-Poster Vercel Deployment Setup")
    print("=" * 50)
    
    # Get Vercel URL from user
    vercel_url = input("Enter your Vercel deployment URL (e.g., https://your-app.vercel.app): ").strip()
    
    if not vercel_url:
        print("❌ Vercel URL is required")
        return
    
    # Remove trailing slash
    vercel_url = vercel_url.rstrip('/')
    
    print(f"\n🔧 Setting up deployment for: {vercel_url}")
    
    # Test deployment
    print("\n1. Testing Vercel deployment...")
    if not test_vercel_deployment(vercel_url):
        print("❌ Deployment test failed. Please check your Vercel deployment.")
        return
    
    # Get current webhook info
    print("\n2. Checking current webhook...")
    get_webhook_info()
    
    # Setup webhook
    print("\n3. Setting up Telegram webhook...")
    if setup_telegram_webhook(vercel_url):
        print("✅ Webhook setup completed!")
    else:
        print("❌ Webhook setup failed")
        return
    
    # Verify webhook
    print("\n4. Verifying webhook setup...")
    webhook_info = get_webhook_info()
    
    if webhook_info and webhook_info.get('url') == f"{vercel_url}/api/webhook":
        print("✅ Webhook verification successful!")
        print("\n🎉 Deployment setup completed!")
        print(f"\n📱 Your bot is now live at: {vercel_url}")
        print("Send a message to your Telegram bot to test it!")
    else:
        print("❌ Webhook verification failed")

if __name__ == "__main__":
    main()
