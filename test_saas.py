#!/usr/bin/env python3
"""
Test script for K-Poster SaaS functionality
Tests user registration, payments, and database operations
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.user_service import user_service
from services.payment_service import payment_service
from services.social_auth_service import social_auth_service
from database.connection import get_db_context
from models.database import User, Transaction, Post
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_user_registration():
    """Test user registration functionality"""
    logger.info("🧪 Testing user registration...")
    
    # Test user data
    telegram_user_id = "test_user_123"
    user_data = {
        'username': 'testuser',
        'first_name': 'Test',
        'last_name': 'User'
    }
    
    # Register user
    user = user_service.get_or_create_user(telegram_user_id, user_data)
    logger.info(f"✅ User registered: {user.id} - {user.first_name} {user.last_name}")
    
    # Get user stats
    stats = user_service.get_user_stats(user.id)
    logger.info(f"📊 User stats: {stats}")
    
    return user

def test_payment_system(user):
    """Test payment system functionality"""
    logger.info("🧪 Testing payment system...")
    
    # Check initial balance
    initial_balance = user_service.get_user_balance(user.id)
    logger.info(f"💰 Initial balance: ₹{initial_balance}")
    
    # Add funds
    add_result = payment_service.add_funds(user.id, 100.0, "manual")
    logger.info(f"💳 Add funds result: {add_result}")
    
    # Check new balance
    new_balance = user_service.get_user_balance(user.id)
    logger.info(f"💰 New balance: ₹{new_balance}")
    
    # Test post creation check
    can_create = user_service.can_create_post(user.id, 20.0)
    logger.info(f"✅ Can create post: {can_create}")
    
    # Charge for a post
    charge_result = payment_service.charge_for_post(user.id, 20.0, "Test post creation")
    logger.info(f"💸 Charge result: {charge_result}")
    
    # Check final balance
    final_balance = user_service.get_user_balance(user.id)
    logger.info(f"💰 Final balance: ₹{final_balance}")
    
    # Get transaction history
    transactions = payment_service.get_user_transactions(user.id, limit=5)
    logger.info(f"📜 Transaction history: {len(transactions)} transactions")
    for txn in transactions:
        logger.info(f"  - {txn['type']}: ₹{txn['amount']} ({txn['status']})")

def test_social_auth(user):
    """Test social media authentication"""
    logger.info("🧪 Testing social media authentication...")
    
    # Get auth URLs
    linkedin_url = social_auth_service.get_linkedin_auth_url(user.id)
    facebook_url = social_auth_service.get_facebook_auth_url(user.id)
    
    logger.info(f"🔗 LinkedIn auth URL: {linkedin_url[:50]}...")
    logger.info(f"🔗 Facebook auth URL: {facebook_url[:50]}...")
    
    # Check connected accounts
    accounts = social_auth_service.get_user_social_accounts(user.id)
    logger.info(f"📱 Connected accounts: {len(accounts)}")

def test_database_operations():
    """Test database operations"""
    logger.info("🧪 Testing database operations...")
    
    with get_db_context() as session:
        # Count records in each table
        user_count = session.query(User).count()
        transaction_count = session.query(Transaction).count()
        post_count = session.query(Post).count()
        
        logger.info(f"📊 Database stats:")
        logger.info(f"  - Users: {user_count}")
        logger.info(f"  - Transactions: {transaction_count}")
        logger.info(f"  - Posts: {post_count}")

def test_post_creation(user):
    """Test post creation and storage"""
    logger.info("🧪 Testing post creation...")
    
    # Create a test post record
    with get_db_context() as session:
        post = Post(
            user_id=user.id,
            topic="Test AI Topic",
            short_post="This is a test short post about AI",
            long_post="This is a longer test post about artificial intelligence and its impact on society.",
            hashtags=["#AI", "#Technology", "#Innovation"],
            key_insights=["AI is transforming industries", "Machine learning is becoming mainstream"],
            status="posted",
            platforms_posted=["linkedin", "facebook"],
            cost=20.0
        )
        
        session.add(post)
        session.commit()
        session.refresh(post)
        
        logger.info(f"📝 Created post: {post.id} - {post.topic}")
        logger.info(f"  - Platforms: {post.platforms_posted}")
        logger.info(f"  - Cost: ₹{post.cost}")

def cleanup_test_data():
    """Clean up test data"""
    logger.info("🧹 Cleaning up test data...")
    
    with get_db_context() as session:
        # Delete test user and related data
        test_user = session.query(User).filter(User.telegram_user_id == "test_user_123").first()
        if test_user:
            # Delete related records first (due to foreign key constraints)
            session.query(Transaction).filter(Transaction.user_id == test_user.id).delete()
            session.query(Post).filter(Post.user_id == test_user.id).delete()
            
            # Delete user
            session.delete(test_user)
            session.commit()
            
            logger.info("✅ Test data cleaned up")
        else:
            logger.info("ℹ️  No test data to clean up")

def run_all_tests():
    """Run all SaaS functionality tests"""
    logger.info("🚀 Starting K-Poster SaaS Tests...")
    
    try:
        # Test database operations
        test_database_operations()
        
        # Test user registration
        user = test_user_registration()
        
        # Test payment system
        test_payment_system(user)
        
        # Test social auth
        test_social_auth(user)
        
        # Test post creation
        test_post_creation(user)
        
        # Final database check
        test_database_operations()
        
        logger.info("✅ All tests completed successfully!")
        
        # Ask if user wants to clean up
        cleanup = input("\n🧹 Clean up test data? (y/n): ").lower().strip()
        if cleanup == 'y':
            cleanup_test_data()
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="K-Poster SaaS Test Suite")
    parser.add_argument("--cleanup", action="store_true", help="Only clean up test data")
    parser.add_argument("--user", action="store_true", help="Test user registration only")
    parser.add_argument("--payment", action="store_true", help="Test payment system only")
    parser.add_argument("--social", action="store_true", help="Test social auth only")
    parser.add_argument("--database", action="store_true", help="Test database operations only")
    
    args = parser.parse_args()
    
    if args.cleanup:
        cleanup_test_data()
    elif args.user:
        test_user_registration()
    elif args.payment:
        user = test_user_registration()
        test_payment_system(user)
    elif args.social:
        user = test_user_registration()
        test_social_auth(user)
    elif args.database:
        test_database_operations()
    else:
        run_all_tests()
